"""
Shared access to a singleton CachedNLICalculator per model.
"""
from typing import Dict
from core.nli_calculator import CachedNLICalculator

_nli_calculators: Dict[str, CachedNLICalculator] = {}

def get_nli_calculator(model_name: str) -> CachedNLICalculator:
    if model_name not in _nli_calculators:
        _nli_calculators[model_name] = CachedNLICalculator(model_name, verbose=False)
    return _nli_calculators[model_name]

