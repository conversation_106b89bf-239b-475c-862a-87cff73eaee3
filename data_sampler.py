import os
import json
import csv
import random
import pandas as pd
import glob
from typing import List, Dict, Any
from pymongo import MongoClient

class DataSampler:
    """数据采样器，负责从不同数据源采样数据"""
    
    def __init__(self):
        """初始化数据采样器"""
        pass
    
    def sample_semeval_data(self, sample_size: int = 500) -> List[Dict[str, Any]]:
        """从SemEval数据集采样数据"""
        print(f"Sampling {sample_size} records from SemEval dataset...")
        
        # 读取CSV文件
        df = pd.read_csv('data/SemEval2017-task4-test.subtask-A.english.csv')
        
        # 随机采样
        sampled_df = df.sample(n=min(sample_size, len(df)), random_state=42)
        
        # 转换为字典列表
        sampled_data = []
        for _, row in sampled_df.iterrows():
            sampled_data.append({
                'id': str(row['id']),
                'text': row['text'],
                'label': row['label']
            })
        
        # 保存采样数据到CSV
        sampled_df.to_csv('sampled_semeval.csv', index=False)
        print(f"Saved {len(sampled_data)} SemEval samples to sampled_semeval.csv")
        
        return sampled_data
    
    def sample_commits_data(self, sample_size: int = 500) -> List[Dict[str, Any]]:
        """从MongoDB中的PyTorch commits数据集采样数据"""
        print(f"Sampling {sample_size} records from MongoDB PyTorch commits dataset...")
        
        # 连接MongoDB
        client = MongoClient('localhost', 27017)
        db = client['pytorch-sha']
        collection = db['pytorch-commits']
        
        # 获取总文档数
        total_count = collection.count_documents({})
        print(f"Total commits in database: {total_count}")
        
        # 使用聚合管道随机采样
        pipeline = [
            {"$sample": {"size": sample_size}}
        ]
        
        sampled_data = []
        for doc in collection.aggregate(pipeline):
            # 提取需要的信息
            sampled_data.append({
                'sha': doc.get('sha', ''),
                'message': doc.get('commit', {}).get('message', ''),
                'date': doc.get('commit', {}).get('author', {}).get('date', ''),
                'author': doc.get('commit', {}).get('author', {}).get('name', '')
            })
        
        # 关闭数据库连接
        client.close()
        
        # 保存采样数据到CSV
        with open('sampled_commits.csv', 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=['sha', 'message', 'date', 'author'])
            writer.writeheader()
            writer.writerows(sampled_data)
        
        print(f"Saved {len(sampled_data)} commit samples to sampled_commits.csv")
        return sampled_data
    
    def run_sampling(self, semeval_sample_size: int = 500, commits_sample_size: int = 500):
        """运行数据采样流程"""
        print("Starting data sampling...")
        
        # 采样SemEval数据
        semeval_data = self.sample_semeval_data(semeval_sample_size)
        
        # 采样Commits数据
        commits_data = self.sample_commits_data(commits_sample_size)
        
        print(f"\nSampling completed!")
        print(f"- SemEval: {len(semeval_data)} samples")
        print(f"- Commits: {len(commits_data)} samples")
        
        return semeval_data, commits_data

def main():
    """主函数 - 仅运行数据采样"""
    sampler = DataSampler()
    sampler.run_sampling()

if __name__ == "__main__":
    main()
