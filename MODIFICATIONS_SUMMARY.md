# LLM响应生成器修改总结

## 修改概述

本次修改主要实现了以下功能：

1. **启用qwen-32b的thinking模式**
2. **新增thinking内容提取功能**
3. **实现新的prompt随机采样逻辑**
4. **新增相应的数据字段保存**

## 详细修改内容

### 1. 启用thinking模式

**文件**: `llm_response_generator.py`
**位置**: `call_llm`方法第115行

```python
# 修改前
extra_body={"enable_thinking": False}

# 修改后  
extra_body={"enable_thinking": True}
```

### 2. 新增thinking内容提取

**文件**: `llm_response_generator.py`
**新增方法**: `extract_thinking_content`

```python
def extract_thinking_content(self, raw_response: str) -> Dict[str, str]:
    """从qwen响应中提取thinking部分和实际回答部分"""
    # 使用正则表达式提取<thinking>...</thinking>标签内容
    # 返回thinking部分和去除thinking后的实际回答
```

### 3. 修改prompt加载逻辑

**文件**: `llm_response_generator.py`
**方法**: `load_prompt_templates`

- **修改前**: 加载固定的3个prompt变体文件
- **修改后**: 加载10个同义prompt文件
  - `prompts/1_sentiment_analysis/prompt_01.txt` 到 `prompt_10.txt`
  - `prompts/2_explorative_coding_commits/prompt_01.txt` 到 `prompt_10.txt`

### 4. 新增prompt采样逻辑

**文件**: `llm_response_generator.py`
**新增方法**: `sample_prompts_for_message`

```python
def sample_prompts_for_message(self, dataset_source: str, message_index: int) -> List[int]:
    """为指定message采样5个prompt索引"""
    # 使用message索引作为随机种子，确保可重现性
    # 从10个prompt中随机选择5个
```

### 5. 修改数据处理流程

**文件**: `llm_response_generator.py`
**方法**: `process_semeval_data`, `process_commits_data`

#### 主要变更：
- **采样逻辑**: 每个message随机选择5个prompt，每个prompt重复8次（总共40次）
- **随机种子**: 使用message在数据集中的索引作为随机种子
- **新的循环结构**: 
  ```
  for message in data:
      selected_prompts = sample_prompts_for_message(dataset, message_index)
      for prompt_index in selected_prompts:
          for attempt in range(8):
              # 调用LLM并保存结果
  ```

### 6. 新增MongoDB数据字段

**新增字段**:
- `thinking_content`: qwen模型的thinking过程内容
- `actual_response`: 去除thinking部分后的实际回答
- `prompt_seed`: 随机数种子（message索引）
- `prompt_index`: 选中的prompt序号（1-10）
- `prompt_raw_text`: 完整的prompt文本内容

**修改字段**:
- `prompt_variant`: 统一设置为"sampled"
- `generation_config.enable_thinking`: 改为True

### 7. 更新环境检查逻辑

**文件**: `run_llm_generation.py`
**方法**: `check_requirements`

- **修改前**: 检查固定的prompt文件
- **修改后**: 检查新的prompt目录结构和10个prompt文件

## 数据流程变化

### 修改前的流程：
```
Message → 3个固定prompt变体 → 每个变体8次 → 总共24次调用
```

### 修改后的流程：
```
Message → 随机选择5个prompt → 每个prompt 8次 → 总共40次调用
├── 使用message索引作为随机种子
├── 提取thinking内容
└── 保存新的数据字段
```

## 向后兼容性

- 保留了原有的`parsed_answer`和`parsed_reason`字段
- 新增字段不影响现有查询逻辑
- 可以通过`prompt_variant`字段区分新旧数据（新数据为"sampled"）

## 测试验证

创建了`test_modifications.py`测试脚本，验证了：
- ✅ Prompt文件加载功能
- ✅ 随机采样逻辑（可重现性）
- ✅ Thinking内容提取功能

## 使用方法

修改后的使用方法与之前相同：

```bash
# 运行完整流程
python run_llm_generation.py --full

# 仅运行LLM响应生成
python run_llm_generation.py --generate

# 测试模式
python run_llm_generation.py --generate --test-mode
```

## 注意事项

1. **随机性**: 每个message的prompt选择是确定的（基于索引种子），确保结果可重现
2. **数据量**: 每个message的调用次数从24次增加到40次
3. **新字段**: MongoDB文档结构有所扩展，包含更多元数据
4. **Thinking模式**: 需要确保qwen-32b模型支持thinking模式
