import pandas as pd
import argparse
import re
from typing import List, <PERSON><PERSON>, Dict, Any, NamedTuple
from tqdm import tqdm
import logging
import hashlib
import pickle
import os
import numpy as np

# 导入统一的NLI计算器
from core.nli_calculator import NLICalculator, CachedNLICalculator, NLIResult

# 导入配置管理和报告生成
from core.config_manager import get_config_manager
from core.report_generator import ReportGenerator, generate_pivot_tables

# 导入UQ方法实现 - NLI版本
from uq_methods.implementations.deg_mat_nli_entail import DegMatNLIEntailUQ
from uq_methods.implementations.eccentricity_nli_entail import EccentricityNLIEntailUQ
from uq_methods.implementations.eig_val_laplacian_nli_entail import EigValLaplacianNLIEntailUQ

# 导入UQ方法实现 - Jaccard版本
from uq_methods.implementations.deg_mat_jaccard import DegMatJaccardUQ
from uq_methods.implementations.eccentricity_jaccard import EccentricityJaccardUQ
from uq_methods.implementations.eig_val_laplacian_jaccard import EigValLaplacianJaccardUQ

# 导入NumSets方法
from uq_methods.implementations.num_sets import NumSetsUQ

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 全局缓存
SIMILARITY_CACHE = {}
NLI_CACHE = {}
NLI_CSV_CACHE = {}  # CSV格式的NLI缓存
CACHE_FILE = "data/similarity_cache.pkl"
NLI_CACHE_DIR = "cache"
NLI_CSV_CACHE_FILE = os.path.join("cache", "nli_results_cache.csv")
OUTPUT_DIR = "data/Uq_Evaluation_20250731"

def create_directories():
    """创建必要的目录结构"""
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    os.makedirs(NLI_CACHE_DIR, exist_ok=True)
    os.makedirs(os.path.dirname(CACHE_FILE), exist_ok=True)
    logger.info(f"Created directories: {OUTPUT_DIR}, {NLI_CACHE_DIR}")

def load_nli_csv_cache():
    """加载CSV格式的NLI缓存"""
    global NLI_CSV_CACHE
    if os.path.exists(NLI_CSV_CACHE_FILE):
        try:
            import pandas as pd
            df = pd.read_csv(NLI_CSV_CACHE_FILE)
            logger.info(f"Loading NLI CSV cache from {NLI_CSV_CACHE_FILE} with {len(df)} rows")

            # 将DataFrame转换为字典格式，使用复合键
            NLI_CSV_CACHE = {}
            invalid_entries = 0

            for idx, row in df.iterrows():
                try:
                    # 验证必需字段
                    required_fields = ['text1_hash', 'text2_hash', 'model_name', 'text1', 'text2']
                    missing_fields = [field for field in required_fields if pd.isna(row.get(field))]

                    if missing_fields:
                        logger.warning(f"Row {idx}: Missing required fields {missing_fields}, skipping")
                        invalid_entries += 1
                        continue

                    key = f"{row['text1_hash']}||{row['text2_hash']}||{row['model_name']}"
                    cache_entry = {
                        'text1': str(row['text1']),
                        'text2': str(row['text2']),
                        'model_name': str(row['model_name']),
                        'text1_hash': str(row['text1_hash']),
                        'text2_hash': str(row['text2_hash']),
                        'timestamp': str(row.get('timestamp', ''))
                    }

                    # 处理新格式（三个分数）和旧格式（单个similarity_score）的兼容性
                    if all(field in row and not pd.isna(row[field]) for field in ['entailment', 'neutral', 'contradiction']):
                        # 新格式：包含三个分数
                        cache_entry.update({
                            'entailment': float(row['entailment']),
                            'neutral': float(row['neutral']),
                            'contradiction': float(row['contradiction'])
                        })
                    elif 'similarity_score' in row and not pd.isna(row['similarity_score']):
                        # 旧格式：只有similarity_score，假设是entailment分数
                        cache_entry.update({
                            'entailment': float(row['similarity_score']),
                            'neutral': 0.33,  # 默认值
                            'contradiction': 0.33  # 默认值
                        })
                    else:
                        # 如果都没有，使用均匀分布
                        logger.warning(f"Row {idx}: No valid NLI scores found, using uniform distribution")
                        cache_entry.update({
                            'entailment': 0.33,
                            'neutral': 0.34,
                            'contradiction': 0.33
                        })

                    NLI_CSV_CACHE[key] = cache_entry

                except Exception as row_error:
                    logger.error(f"Error processing row {idx}: {row_error}")
                    invalid_entries += 1
                    continue

            logger.info(f"Loaded NLI CSV cache with {len(NLI_CSV_CACHE)} valid entries from {NLI_CSV_CACHE_FILE}")
            if invalid_entries > 0:
                logger.warning(f"Skipped {invalid_entries} invalid entries during cache loading")

        except Exception as e:
            logger.error(f"Failed to load NLI CSV cache: {e}")
            import traceback
            logger.debug(f"Traceback: {traceback.format_exc()}")
            NLI_CSV_CACHE = {}
    else:
        logger.info(f"NLI CSV cache file {NLI_CSV_CACHE_FILE} does not exist, starting with empty cache")
        NLI_CSV_CACHE = {}

def save_nli_csv_cache():
    """保存CSV格式的NLI缓存"""
    try:
        import pandas as pd
        import time

        if not NLI_CSV_CACHE:
            return
        
        # 转换字典为DataFrame
        rows = []
        for key, data in NLI_CSV_CACHE.items():
            row = {
                'text1': data['text1'],
                'text2': data['text2'],
                'model_name': data['model_name'],
                'text1_hash': data['text1_hash'],
                'text2_hash': data['text2_hash'],
                'timestamp': data['timestamp']
            }
            
            # 添加三个NLI分数
            if 'entailment' in data and 'neutral' in data and 'contradiction' in data:
                row.update({
                    'entailment': data['entailment'],
                    'neutral': data['neutral'],
                    'contradiction': data['contradiction']
                })
            elif 'similarity_score' in data:
                # 向后兼容：将similarity_score作为entailment分数
                row.update({
                    'entailment': data['similarity_score'],
                    'neutral': 0.33,
                    'contradiction': 0.33
                })
            else:
                # 默认均匀分布
                row.update({
                    'entailment': 0.33,
                    'neutral': 0.34,
                    'contradiction': 0.33
                })
            
            rows.append(row)
        
        df = pd.DataFrame(rows)
        os.makedirs(os.path.dirname(NLI_CSV_CACHE_FILE), exist_ok=True)
        df.to_csv(NLI_CSV_CACHE_FILE, index=False, encoding='utf-8-sig')
        logger.info(f"Saved NLI CSV cache with {len(rows)} entries to {NLI_CSV_CACHE_FILE}")
    except Exception as e:
        logger.warning(f"Failed to save NLI CSV cache: {e}")

def save_nli_csv_cache_with_timestamp():
    """带时间戳保存CSV格式的NLI缓存"""
    try:
        import pandas as pd
        import time

        if not NLI_CSV_CACHE:
            return

        timestamp = time.strftime("%Y%m%d_%H%M%S")
        timestamped_file = os.path.join(NLI_CACHE_DIR, f"nli_results_cache_{timestamp}.csv")

        # 转换字典为DataFrame
        rows = []
        for key, data in NLI_CSV_CACHE.items():
            row = {
                'text1': data['text1'],
                'text2': data['text2'],
                'model_name': data['model_name'],
                'text1_hash': data['text1_hash'],
                'text2_hash': data['text2_hash'],
                'timestamp': data['timestamp']
            }

            # 添加三个NLI分数，与save_nli_csv_cache()保持一致
            if 'entailment' in data and 'neutral' in data and 'contradiction' in data:
                row.update({
                    'entailment': data['entailment'],
                    'neutral': data['neutral'],
                    'contradiction': data['contradiction']
                })
            elif 'similarity_score' in data:
                # 向后兼容：将similarity_score作为entailment分数
                row.update({
                    'entailment': data['similarity_score'],
                    'neutral': 0.33,
                    'contradiction': 0.33
                })
            else:
                # 默认均匀分布
                row.update({
                    'entailment': 0.33,
                    'neutral': 0.34,
                    'contradiction': 0.33
                })

            rows.append(row)

        df = pd.DataFrame(rows)
        os.makedirs(os.path.dirname(timestamped_file), exist_ok=True)
        df.to_csv(timestamped_file, index=False, encoding='utf-8-sig')
        logger.info(f"Saved timestamped NLI CSV cache with {len(rows)} entries to {timestamped_file}")
    except Exception as e:
        logger.warning(f"Failed to save timestamped NLI CSV cache: {e}")
        import traceback
        logger.debug(f"Traceback: {traceback.format_exc()}")

def load_cache():
    """加载缓存文件"""
    global SIMILARITY_CACHE, NLI_CACHE
    if os.path.exists(CACHE_FILE):
        try:
            with open(CACHE_FILE, 'rb') as f:
                SIMILARITY_CACHE = pickle.load(f)
            logger.info(f"Loaded similarity cache with {len(SIMILARITY_CACHE)} entries")
        except Exception as e:
            logger.warning(f"Failed to load cache: {e}")
            SIMILARITY_CACHE = {}
    else:
        SIMILARITY_CACHE = {}
    
    # 加载NLI缓存
    load_nli_cache()
    # 加载CSV格式的NLI缓存
    load_nli_csv_cache()

def save_cache():
    """保存缓存文件"""
    try:
        os.makedirs(os.path.dirname(CACHE_FILE), exist_ok=True)
        with open(CACHE_FILE, 'wb') as f:
            pickle.dump(SIMILARITY_CACHE, f)
        logger.info(f"Saved similarity cache with {len(SIMILARITY_CACHE)} entries")
    except Exception as e:
        logger.warning(f"Failed to save cache: {e}")
    
    # 保存NLI缓存
    save_nli_cache()
    # 保存CSV格式的NLI缓存
    save_nli_csv_cache()

def load_nli_cache():
    """加载NLI缓存文件"""
    global NLI_CACHE
    nli_cache_file = os.path.join(NLI_CACHE_DIR, "nli_cache.pkl")
    if os.path.exists(nli_cache_file):
        try:
            with open(nli_cache_file, 'rb') as f:
                NLI_CACHE = pickle.load(f)
            logger.info(f"Loaded NLI cache with {len(NLI_CACHE)} entries")
        except Exception as e:
            logger.warning(f"Failed to load NLI cache: {e}")
            NLI_CACHE = {}
    else:
        NLI_CACHE = {}

def save_nli_cache():
    """保存NLI缓存文件"""
    try:
        os.makedirs(NLI_CACHE_DIR, exist_ok=True)
        nli_cache_file = os.path.join(NLI_CACHE_DIR, "nli_cache.pkl")
        with open(nli_cache_file, 'wb') as f:
            pickle.dump(NLI_CACHE, f)
        logger.info(f"Saved NLI cache with {len(NLI_CACHE)} entries to {nli_cache_file}")
    except Exception as e:
        logger.warning(f"Failed to save NLI cache: {e}")

def save_nli_cache_with_timestamp():
    """带时间戳保存NLI缓存文件"""
    try:
        import time
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        os.makedirs(NLI_CACHE_DIR, exist_ok=True)
        nli_cache_file = os.path.join(NLI_CACHE_DIR, f"nli_cache_{timestamp}.pkl")
        with open(nli_cache_file, 'wb') as f:
            pickle.dump(NLI_CACHE, f)
        logger.info(f"Saved timestamped NLI cache with {len(NLI_CACHE)} entries to {nli_cache_file}")
    except Exception as e:
        logger.warning(f"Failed to save timestamped NLI cache: {e}")

def get_cache_key(text1: str, text2: str, method_type: str) -> str:
    """生成缓存键"""
    # 使用哈希来处理长文本，确保键的唯一性
    combined = f"{text1}||{text2}||{method_type}"
    return hashlib.md5(combined.encode()).hexdigest()

def cached_jaccard_similarity(text1: str, text2: str) -> float:
    """带缓存的Jaccard相似度计算"""
    cache_key = get_cache_key(text1, text2, "jaccard")
    
    if cache_key in SIMILARITY_CACHE:
        return SIMILARITY_CACHE[cache_key]
    
    # 计算Jaccard相似度
    words1 = set(text1.lower().split())
    words2 = set(text2.lower().split())
    intersection = len(words1.intersection(words2))
    union = len(words1.union(words2))
    similarity = intersection / union if union > 0 else 0.0
    
    # 缓存结果
    SIMILARITY_CACHE[cache_key] = similarity
    return similarity

def get_text_hash(text: str) -> str:
    """生成文本的哈希值"""
    return hashlib.md5(text.encode('utf-8')).hexdigest()

# 全局NLI计算器
_nli_calculators = {}

def get_nli_calculator(model_name: str) -> CachedNLICalculator:
    """获取NLI计算器实例（单例模式）"""
    if model_name not in _nli_calculators:
        _nli_calculators[model_name] = CachedNLICalculator(model_name, verbose=True)
    return _nli_calculators[model_name]

def cached_nli_scores(text1: str, text2: str, model_name: str) -> NLIResult:
    """带缓存的完整NLI分数计算"""
    # 生成文本哈希和缓存键
    text1_hash = get_text_hash(text1)
    text2_hash = get_text_hash(text2)
    csv_cache_key = f"{text1_hash}||{text2_hash}||{model_name}"
    old_cache_key = get_cache_key(text1, text2, f"nli_{model_name}")
    
    # 首先检查CSV缓存
    if csv_cache_key in NLI_CSV_CACHE:
        cached_data = NLI_CSV_CACHE[csv_cache_key]
        return NLIResult(
            entailment=cached_data['entailment'],
            neutral=cached_data['neutral'],
            contradiction=cached_data['contradiction']
        )
    
    # 然后检查旧的NLI缓存（向后兼容）
    if old_cache_key in NLI_CACHE:
        similarity = NLI_CACHE[old_cache_key]
        # 将结果迁移到CSV缓存，假设similarity是entailment分数
        import time
        NLI_CSV_CACHE[csv_cache_key] = {
            'text1': text1,
            'text2': text2,
            'model_name': model_name,
            'entailment': similarity,
            'neutral': 0.33,
            'contradiction': 0.33,
            'text1_hash': text1_hash,
            'text2_hash': text2_hash,
            'timestamp': time.strftime("%Y-%m-%d %H:%M:%S")
        }
        return NLIResult(entailment=similarity, neutral=0.33, contradiction=0.33)
    
    # 检查通用缓存（向后兼容）
    if old_cache_key in SIMILARITY_CACHE:
        similarity = SIMILARITY_CACHE[old_cache_key]
        # 将结果迁移到CSV缓存
        import time
        NLI_CSV_CACHE[csv_cache_key] = {
            'text1': text1,
            'text2': text2,
            'model_name': model_name,
            'entailment': similarity,
            'neutral': 0.33,
            'contradiction': 0.33,
            'text1_hash': text1_hash,
            'text2_hash': text2_hash,
            'timestamp': time.strftime("%Y-%m-%d %H:%M:%S")
        }
        return NLIResult(entailment=similarity, neutral=0.33, contradiction=0.33)
    
    # 计算新的NLI分数
    logger.info(f"Computing new NLI scores for model {model_name}")
    nli_calculator = get_nli_calculator(model_name)
    nli_result = nli_calculator.compute_nli_scores(text1, text2)
    
    # 保存到CSV缓存
    import time
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
    
    NLI_CSV_CACHE[csv_cache_key] = {
        'text1': text1,
        'text2': text2,
        'model_name': model_name,
        'entailment': nli_result.entailment,
        'neutral': nli_result.neutral,
        'contradiction': nli_result.contradiction,
        'text1_hash': text1_hash,
        'text2_hash': text2_hash,
        'timestamp': timestamp
    }
    
    return nli_result

def cached_nli_similarity(text1: str, text2: str, nli_model) -> float:
    """带缓存的NLI相似度计算（向后兼容，返回entailment分数）"""
    model_name = nli_model.model_name if hasattr(nli_model, 'model_name') else str(nli_model)
    nli_result = cached_nli_scores(text1, text2, model_name)
    return nli_result.entailment

class CachedUQMethods:
    """包装UQ方法，使用统一的缓存NLI计算"""
    
    def __init__(self, nli_model_name: str = "microsoft/deberta-large-mnli"):
        self.nli_model_name = nli_model_name
        
        # 使用统一的NLI计算器
        self.nli_calculator = get_nli_calculator(nli_model_name)
        
        # 初始化所有UQ方法
        self.methods = {
            # NLI版本 - 使用统一计算器
            'deg_mat_nli': self._create_unified_nli_method("deg_mat", nli_model_name),
            'eccentricity_nli': self._create_unified_nli_method("eccentricity", nli_model_name),
            'eig_val_nli': self._create_unified_nli_method("eig_val", nli_model_name),
            
            # Jaccard版本 - 使用缓存
            'deg_mat_jaccard': self._create_cached_jaccard_method(DegMatJaccardUQ),
            'eccentricity_jaccard': self._create_cached_jaccard_method(EccentricityJaccardUQ),
            'eig_val_jaccard': self._create_cached_jaccard_method(EigValLaplacianJaccardUQ),
            
            # NumSets方法 - 强制使用统一NLI系统
            'num_sets': NumSetsUQ(model_name=nli_model_name, use_unified_nli=True)
        }
    
    def _create_unified_nli_method(self, method_type: str, model_name: str):
        """创建使用统一NLI计算器的方法实例"""
        
        class UnifiedNLIMethod:
            def __init__(self, method_type, model_name):
                self.method_type = method_type
                self.model_name = model_name
                self.verbose = False
                self.nli_calculator = get_nli_calculator(model_name)
            
            def _compute_similarity_matrix(self, responses: List[str]):
                """使用统一NLI计算器计算相似度矩阵"""
                return self.nli_calculator.compute_similarity_matrix_cached(responses, use_score="entailment")
            
            def compute_uncertainty(self, responses: List[str]) -> Dict[str, Any]:
                """计算不确定性度量"""
                try:
                    W = self._compute_similarity_matrix(responses)
                    
                    if self.method_type == "deg_mat":
                        return self._compute_deg_mat_uncertainty(W)
                    elif self.method_type == "eccentricity":
                        return self._compute_eccentricity_uncertainty(W)
                    elif self.method_type == "eig_val":
                        return self._compute_eigenvalue_uncertainty(W)
                        
                except Exception as e:
                    logger.error(f"Error in {self.method_type} uncertainty computation: {str(e)}")
                    return {"error": str(e)}
            
            def _compute_deg_mat_uncertainty(self, W):
                """计算度矩阵不确定性"""
                import numpy as np
                degrees = np.sum(W, axis=1)
                uncertainty_score = np.sum(degrees)
                return {
                    "uncertainty_score": float(uncertainty_score),
                    "mean_similarity": float(np.mean(W[W != 1.0])) if len(W[W != 1.0]) > 0 else 0.0,
                    "method": f"deg_mat_nli"
                }
            
            def _compute_eccentricity_uncertainty(self, W):
                """计算偏心率不确定性"""
                import numpy as np
                from scipy.linalg import eigh
                
                # 计算图拉普拉斯矩阵
                D = np.diag(np.sum(W, axis=1))
                L = D - W
                
                # 计算特征值和特征向量
                eigenvals, eigenvecs = eigh(L)
                
                # 计算偏心率（特征向量的范数）
                eccentricity = np.linalg.norm(eigenvecs, axis=0)
                uncertainty_score = float(np.mean(eccentricity))
                
                return {
                    "uncertainty_score": uncertainty_score,
                    "mean_similarity": float(np.mean(W[W != 1.0])) if len(W[W != 1.0]) > 0 else 0.0,
                    "method": f"eccentricity_nli"
                }
            
            def _compute_eigenvalue_uncertainty(self, W):
                """计算特征值不确定性"""
                import numpy as np
                from scipy.linalg import eigh
                
                # 计算图拉普拉斯矩阵
                D = np.diag(np.sum(W, axis=1))
                L = D - W
                
                # 计算特征值
                eigenvals, _ = eigh(L)
                
                # 计算特征值和作为不确定性度量
                uncertainty_score = float(np.sum(eigenvals))
                
                return {
                    "uncertainty_score": uncertainty_score,
                    "mean_similarity": float(np.mean(W[W != 1.0])) if len(W[W != 1.0]) > 0 else 0.0,
                    "method": f"eig_val_nli"
                }
        
        return UnifiedNLIMethod(method_type, model_name)
    
    def _create_cached_jaccard_method(self, method_class):
        """创建使用缓存的Jaccard方法实例"""
        return method_class()  # Jaccard方法不需要额外的缓存处理

def split_twitter_response(response: str, prompt_type: str) -> Tuple[str, str]:
    """从Twitter响应中提取label和reasoning部分"""
    response = response.replace('\r\n', '\n').replace('\r', '\n')
    if prompt_type == "sentiment":
        return response.strip(), ""
    elif prompt_type in ("sentiment_reason", "sentiment_reason_first"):
        label_match = re.search(r"\[Label\]:\s*([^\n]+)", response)
        reasoning_match = re.search(r"\[Reasoning\]:\s*((?:.|\n)*?)(?=\n\[|$)", response)
        label = label_match.group(1).strip() if label_match else ""
        reasoning = reasoning_match.group(1).strip() if reasoning_match else ""
        return label, reasoning
    else:
        return response.strip(), ""

def split_commit_response(response: str, prompt_type: str) -> Tuple[str, str]:
    """从Commit响应中提取module和reasoning部分"""
    if prompt_type == 'single_word':
        return response.strip(), ""
    else:
        module, reasoning = '', ''
        module_match = re.search(r'Module:\s*(.*?)(?:\n|$)', response, re.DOTALL)
        reasoning_match = re.search(r'Reasoning:\s*(.*?)(?:\nModule:|$)', response, re.DOTALL)
        if module_match:
            module = module_match.group(1).strip()
        if reasoning_match:
            reasoning = reasoning_match.group(1).strip()
        return module, reasoning

def calculate_validation_accuracy(predicted_labels: List[str], validation_labels: List[str]) -> Dict[str, float]:
    """计算预测标签与validation标签的准确率"""
    if len(predicted_labels) != len(validation_labels):
        logger.warning(f"Length mismatch: predicted={len(predicted_labels)}, validation={len(validation_labels)}")
        return {'accuracy': 0.0, 'total_pairs': 0}
    
    correct = 0
    total = len(predicted_labels)
    
    for pred, val in zip(predicted_labels, validation_labels):
        # 简单的字符串匹配（可能需要根据具体情况调整）
        if pred.lower().strip() == val.lower().strip():
            correct += 1
    
    accuracy = correct / total if total > 0 else 0.0
    return {
        'accuracy': accuracy,
        'correct_predictions': correct,
        'total_pairs': total
    }

def validate_input_data(df: pd.DataFrame, data_type: str) -> Tuple[pd.DataFrame, Dict[str, Any]]:
    """验证输入数据的完整性和格式"""
    validation_report = {
        'original_rows': len(df),
        'issues': [],
        'cleaned_rows': 0,
        'dropped_rows': 0
    }

    logger.info(f"Validating {data_type} data with {len(df)} rows")

    # 检查必需的列
    if data_type == 'twitter':
        required_cols = ['tweet_index', 'prompt_type', 'response_text']
        optional_cols = ['validation']
    else:  # commit
        required_cols = ['commit_sha', 'prompt_type', 'response_text']
        optional_cols = []

    # 检查缺失的列
    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        error_msg = f"Missing required columns: {missing_cols}"
        logger.error(error_msg)
        validation_report['issues'].append(error_msg)
        raise ValueError(error_msg)

    # 检查数据类型和缺失值
    initial_len = len(df)

    # 删除response_text为空的行
    df = df.dropna(subset=['response_text'])
    df = df[df['response_text'].str.strip() != '']
    dropped_empty_responses = initial_len - len(df)
    if dropped_empty_responses > 0:
        issue = f"Dropped {dropped_empty_responses} rows with empty response_text"
        logger.warning(issue)
        validation_report['issues'].append(issue)

    # 检查identifier列的数据类型
    identifier_col = 'tweet_index' if data_type == 'twitter' else 'commit_sha'
    df = df.dropna(subset=[identifier_col])
    dropped_empty_ids = initial_len - dropped_empty_responses - len(df)
    if dropped_empty_ids > 0:
        issue = f"Dropped {dropped_empty_ids} rows with empty {identifier_col}"
        logger.warning(issue)
        validation_report['issues'].append(issue)

    # 检查prompt_type的有效性
    valid_prompt_types = {
        'twitter': ['sentiment', 'sentiment_reason', 'sentiment_reason_first'],
        'commit': ['single_word', 'module_reasoning']
    }

    if data_type in valid_prompt_types:
        invalid_prompts = df[~df['prompt_type'].isin(valid_prompt_types[data_type])]
        if len(invalid_prompts) > 0:
            issue = f"Found {len(invalid_prompts)} rows with invalid prompt_type values"
            logger.warning(issue)
            validation_report['issues'].append(issue)
            # 保留这些行，但记录警告

    # 检查数据分布
    logger.info(f"Data distribution by prompt_type:")
    prompt_dist = df['prompt_type'].value_counts()
    for prompt_type, count in prompt_dist.items():
        logger.info(f"  {prompt_type}: {count} rows")

    identifier_dist = df[identifier_col].value_counts()
    logger.info(f"Identifier distribution: {len(identifier_dist)} unique {identifier_col}s")
    logger.info(f"  Min responses per identifier: {identifier_dist.min()}")
    logger.info(f"  Max responses per identifier: {identifier_dist.max()}")
    logger.info(f"  Mean responses per identifier: {identifier_dist.mean():.2f}")

    validation_report['cleaned_rows'] = len(df)
    validation_report['dropped_rows'] = validation_report['original_rows'] - validation_report['cleaned_rows']

    logger.info(f"Data validation complete: {validation_report['cleaned_rows']} valid rows, {validation_report['dropped_rows']} dropped")

    return df, validation_report

def analyze_all_uq_methods(csv_file: str, data_type: str, nli_model: str = "microsoft/deberta-large-mnli",
                          max_identifiers: int = None):
    """分析所有数据的UQ方法"""
    print(f"\n=== Analyzing ALL {data_type} data with UQ methods ===")
    print(f"Using NLI model: {nli_model}")

    # 创建必要的目录
    create_directories()

    # 加载缓存
    load_cache()

    # 读取和验证数据
    try:
        df = pd.read_csv(csv_file)
        print(f"Loaded {len(df)} records from {csv_file}")

        # 验证数据完整性
        df, validation_report = validate_input_data(df, data_type)
        print(f"Data validation: {validation_report['cleaned_rows']} valid rows, {validation_report['dropped_rows']} dropped")

        if validation_report['issues']:
            print("Data validation issues:")
            for issue in validation_report['issues']:
                print(f"  - {issue}")

    except Exception as e:
        logger.error(f"Failed to load or validate data from {csv_file}: {e}")
        raise

    if data_type == 'twitter':
        identifier_col = 'tweet_index'
        sample_sizes = [10, 15, 20]  # 针对Twitter数据的sample sizes
    else:  # commit
        identifier_col = 'commit_sha'
        sample_sizes = [10, 15, 20, 25, 30]  # 针对Commit数据的sample sizes

    # 获取unique identifiers，并可能限制数量
    unique_identifiers = df[identifier_col].unique()
    if max_identifiers:
        unique_identifiers = unique_identifiers[:max_identifiers]
        logger.info(f"Limited to first {max_identifiers} identifiers")

    print(f"Found {len(unique_identifiers)} unique {identifier_col}s to process")
    
    # 初始化缓存的UQ方法
    logger.info("Initializing cached UQ methods...")
    cached_methods = CachedUQMethods(nli_model)
    logger.info(f"Initialized {len(cached_methods.methods)} UQ methods (NLI + Jaccard + NumSets versions)")
    
    results = []
    
    # 按identifier和prompt_type分组
    grouped = df.groupby([identifier_col, 'prompt_type'])
    logger.info(f"Processing {len(grouped)} groups ({identifier_col}, prompt_type combinations)")
    
    processed_count = 0
    for (identifier, prompt_type), group in tqdm(grouped, desc=f"Processing {data_type} groups"):
        # 如果设置了max_identifiers限制，跳过不在列表中的identifier
        if max_identifiers and identifier not in unique_identifiers:
            continue
            
        logger.info(f"Processing {identifier_col}={str(identifier)[:8]}..., prompt_type={prompt_type}")
        
        responses = group['response_text'].dropna().tolist()
        logger.info(f"Found {len(responses)} responses")
        
        if len(responses) < max(sample_sizes):
            logger.warning(f"Skipping: only {len(responses)} responses, need at least {max(sample_sizes)}")
            continue
        
        # 提取labels/modules和validation数据
        if data_type == 'twitter':
            content_items = []
            validation_items = []
            for idx, resp in enumerate(responses):
                label, _ = split_twitter_response(resp, prompt_type)
                if label:
                    content_items.append(label)
                    # 获取对应的validation标签
                    validation_label = group.iloc[idx]['validation'] if idx < len(group) else ""
                    validation_items.append(str(validation_label))
            content_type = 'label'
        else:  # commit
            validation_items = []  # Commit数据没有validation
            if prompt_type == 'single_word':
                content_items = responses
                content_type = 'response'
            else:
                content_items = []
                for resp in responses:
                    module, _ = split_commit_response(resp, prompt_type)
                    if module:
                        content_items.append(module)
                content_type = 'module'
        
        logger.info(f"Extracted {len(content_items)} {content_type}s")
        
        if len(content_items) < max(sample_sizes):
            logger.warning(f"Skipping: only {len(content_items)} {content_type}s available")
            continue
        
        # 对每个sample size进行测试
        for sample_size in sample_sizes:
            if len(content_items) < sample_size:
                logger.warning(f"Skipping sample_size={sample_size}: only {len(content_items)} items available")
                continue
                
            sample_items = content_items[:sample_size]
            sample_validation = validation_items[:sample_size] if validation_items else []
            logger.info(f"Testing with sample_size={sample_size}")
            
            # 计算validation准确率（仅对Twitter数据）
            validation_accuracy = {}
            if data_type == 'twitter' and validation_items:
                validation_accuracy = calculate_validation_accuracy(sample_items, sample_validation)
                logger.info(f"Validation accuracy: {validation_accuracy['accuracy']:.4f} ({validation_accuracy['correct_predictions']}/{validation_accuracy['total_pairs']})")
            
            # 对每个UQ方法进行测试
            for method_name, uq_method in cached_methods.methods.items():
                try:
                    logger.info(f"Computing uncertainty for {len(sample_items)} {content_type}s using {method_name}")

                    # 验证输入数据
                    if not sample_items:
                        logger.warning(f"Empty sample_items for {method_name}, skipping")
                        continue

                    if any(not isinstance(item, str) or not item.strip() for item in sample_items):
                        logger.warning(f"Invalid sample_items detected for {method_name}, filtering empty items")
                        sample_items = [item for item in sample_items if isinstance(item, str) and item.strip()]
                        if len(sample_items) < 2:
                            logger.warning(f"Insufficient valid items after filtering for {method_name}, skipping")
                            continue

                    result = uq_method.compute_uncertainty(sample_items)

                    # 验证结果格式
                    if not isinstance(result, dict):
                        logger.error(f"Invalid result format from {method_name}: expected dict, got {type(result)}")
                        continue

                    # 处理不同方法的返回格式
                    if method_name == 'num_sets':
                        uncertainty_score = result.get('set_entropy', 0)
                        mean_similarity = 0  # NumSets不返回mean_similarity
                        additional_metrics = {
                            'num_sets': result.get('num_sets', 0),
                            'set_sizes': result.get('set_sizes', [])
                        }
                    else:
                        uncertainty_score = result.get('uncertainty_score', 0)
                        mean_similarity = result.get('mean_similarity', 0)
                        additional_metrics = {}

                        # 验证数值有效性
                        if not isinstance(uncertainty_score, (int, float)) or not np.isfinite(uncertainty_score):
                            logger.warning(f"Invalid uncertainty_score from {method_name}: {uncertainty_score}, setting to 0")
                            uncertainty_score = 0

                        if not isinstance(mean_similarity, (int, float)) or not np.isfinite(mean_similarity):
                            logger.warning(f"Invalid mean_similarity from {method_name}: {mean_similarity}, setting to 0")
                            mean_similarity = 0

                    logger.info(f"Result: uncertainty_score={uncertainty_score:.4f}, mean_similarity={mean_similarity:.4f}")

                    result_dict = {
                        'data_type': data_type,
                        'identifier': str(identifier)[:12] if len(str(identifier)) > 12 else str(identifier),
                        'prompt_type': prompt_type,
                        'content_type': content_type,
                        'sample_size': sample_size,
                        'uq_method': method_name,
                        'uncertainty_score': float(uncertainty_score),
                        'mean_similarity': float(mean_similarity),
                        'method_details': result.get('method', method_name),
                        'sample_responses': sample_items[:3]
                    }

                    # 添加validation准确率信息
                    if validation_accuracy:
                        result_dict.update({
                            'validation_accuracy': validation_accuracy['accuracy'],
                            'correct_predictions': validation_accuracy['correct_predictions'],
                            'total_validation_pairs': validation_accuracy['total_pairs']
                        })

                    # 添加额外的指标
                    result_dict.update(additional_metrics)
                    results.append(result_dict)

                except Exception as e:
                    logger.error(f"Error processing {method_name} with sample_size={sample_size}: {str(e)}")
                    import traceback
                    logger.debug(f"Detailed traceback for {method_name}: {traceback.format_exc()}")

                    # 添加错误记录到结果中，便于后续分析
                    error_result = {
                        'data_type': data_type,
                        'identifier': str(identifier)[:12] if len(str(identifier)) > 12 else str(identifier),
                        'prompt_type': prompt_type,
                        'content_type': content_type,
                        'sample_size': sample_size,
                        'uq_method': method_name,
                        'uncertainty_score': np.nan,
                        'mean_similarity': np.nan,
                        'method_details': f"ERROR: {str(e)}",
                        'sample_responses': sample_items[:3] if sample_items else []
                    }

                    if validation_accuracy:
                        error_result.update({
                            'validation_accuracy': validation_accuracy['accuracy'],
                            'correct_predictions': validation_accuracy['correct_predictions'],
                            'total_validation_pairs': validation_accuracy['total_pairs']
                        })

                    results.append(error_result)
        
        processed_count += 1

        # 优化内存使用和缓存管理
        if processed_count % 5 == 0:
            logger.info(f"Processed {processed_count} groups so far...")
            # 定期保存CSV格式的NLI缓存
            save_nli_csv_cache()

            # 内存使用监控
            import psutil
            process = psutil.Process()
            memory_info = process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024
            logger.info(f"Current memory usage: {memory_mb:.1f} MB")

            # 如果内存使用过高，清理一些缓存
            if memory_mb > 4000:  # 4GB阈值
                logger.warning(f"High memory usage detected ({memory_mb:.1f} MB), performing cache cleanup")
                # 清理NLI计算器的内存缓存（保留文件缓存）
                for calc in _nli_calculators.values():
                    if hasattr(calc, 'nli_cache'):
                        cache_size = len(calc.nli_cache)
                        calc.clear_cache()
                        logger.info(f"Cleared NLI calculator cache ({cache_size} entries)")

                # 强制垃圾回收
                import gc
                gc.collect()

                # 检查清理后的内存使用
                memory_info_after = process.memory_info()
                memory_mb_after = memory_info_after.rss / 1024 / 1024
                logger.info(f"Memory usage after cleanup: {memory_mb_after:.1f} MB")

        # 每处理20个组保存一次中间结果
        if processed_count % 20 == 0 and results:
            intermediate_file = os.path.join(OUTPUT_DIR, f'intermediate_results_{data_type}_{processed_count}.csv')
            try:
                intermediate_df = pd.DataFrame(results)
                intermediate_df.to_csv(intermediate_file, index=False, encoding='utf-8-sig')
                logger.info(f"Saved intermediate results to {intermediate_file}")
            except Exception as e:
                logger.warning(f"Failed to save intermediate results: {e}")

    # 最终保存CSV格式的NLI缓存
    save_nli_csv_cache()

    # 最终内存使用报告
    try:
        import psutil
        process = psutil.Process()
        memory_info = process.memory_info()
        memory_mb = memory_info.rss / 1024 / 1024
        logger.info(f"Final memory usage: {memory_mb:.1f} MB")
    except ImportError:
        logger.info("psutil not available for memory monitoring")

    return results

def create_summary_tables(results_df: pd.DataFrame):
    """创建汇总表格，包括validation准确率"""
    print("\n=== Results Summary Tables ===")
    
    for data_type in results_df['data_type'].unique():
        type_df = results_df[results_df['data_type'] == data_type]
        print(f"\n--- {data_type.upper()} Data Summary ---")
        
        # 按prompt_type, sample_size, uq_method展示uncertainty_score
        pivot_uncertainty = type_df.pivot_table(
            index=['prompt_type', 'sample_size'], 
            columns='uq_method', 
            values='uncertainty_score',
            aggfunc='mean'
        ).round(4)
        print("\nUncertainty Scores:")
        print(pivot_uncertainty)
        
        # 按prompt_type, sample_size, uq_method展示mean_similarity
        pivot_similarity = type_df.pivot_table(
            index=['prompt_type', 'sample_size'], 
            columns='uq_method', 
            values='mean_similarity',
            aggfunc='mean'
        ).round(4)
        print("\nMean Similarity Scores:")
        print(pivot_similarity)
        
        # 对于Twitter数据，展示validation准确率
        if data_type == 'twitter' and 'validation_accuracy' in type_df.columns:
            pivot_validation = type_df.pivot_table(
                index=['prompt_type', 'sample_size'], 
                columns='uq_method', 
                values='validation_accuracy',
                aggfunc='mean'
            ).round(4)
            print("\nValidation Accuracy:")
            print(pivot_validation)
            
            # 保存validation准确率表
            validation_file = os.path.join(OUTPUT_DIR, f'{data_type}_validation_accuracy_pivot.csv')
            pivot_validation.to_csv(validation_file)
            print(f"Validation accuracy table saved to {validation_file}")
        
        # 保存详细的pivot表到CSV
        uncertainty_file = os.path.join(OUTPUT_DIR, f'{data_type}_uncertainty_pivot.csv')
        similarity_file = os.path.join(OUTPUT_DIR, f'{data_type}_similarity_pivot.csv')
        pivot_uncertainty.to_csv(uncertainty_file)
        pivot_similarity.to_csv(similarity_file)
        print(f"\nDetailed pivot tables saved to {OUTPUT_DIR}/{data_type}_*_pivot.csv")
        
        # 创建方法比较表（NLI vs Jaccard）
        print(f"\n--- Method Comparison ({data_type.upper()}) ---")
        for method_base in ['deg_mat', 'eccentricity', 'eig_val']:
            nli_method = f'{method_base}_nli'
            jaccard_method = f'{method_base}_jaccard'
            
            if nli_method in type_df['uq_method'].values and jaccard_method in type_df['uq_method'].values:
                comparison_df = type_df[type_df['uq_method'].isin([nli_method, jaccard_method])]
                
                # 基本指标比较
                comparison_pivot = comparison_df.pivot_table(
                    index=['prompt_type', 'sample_size'],
                    columns='uq_method',
                    values=['uncertainty_score', 'mean_similarity'],
                    aggfunc='mean'
                ).round(4)
                print(f"\n{method_base.upper()} Comparison (NLI vs Jaccard):")
                print(comparison_pivot)
                
                # 如果有validation数据，也进行比较
                if data_type == 'twitter' and 'validation_accuracy' in comparison_df.columns:
                    validation_comparison = comparison_df.pivot_table(
                        index=['prompt_type', 'sample_size'],
                        columns='uq_method',
                        values='validation_accuracy',
                        aggfunc='mean'
                    ).round(4)
                    print(f"\n{method_base.upper()} Validation Accuracy Comparison:")
                    print(validation_comparison)

def load_checkpoint(checkpoint_file):
    """加载检查点文件，用于恢复中断的处理"""
    if os.path.exists(checkpoint_file):
        try:
            with open(checkpoint_file, 'rb') as f:
                checkpoint = pickle.load(f)
            logger.info(f"Loaded checkpoint from {checkpoint_file}")
            return checkpoint
        except Exception as e:
            logger.error(f"Failed to load checkpoint: {e}")
    return None

def save_checkpoint(checkpoint_data, checkpoint_file):
    """保存检查点文件，用于中断恢复"""
    try:
        with open(checkpoint_file, 'wb') as f:
            pickle.dump(checkpoint_data, f)
        logger.info(f"Saved checkpoint to {checkpoint_file}")
        return True
    except Exception as e:
        logger.error(f"Failed to save checkpoint: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description="Analyze UQ methods on ALL Twitter and Commit data with validation accuracy")
    parser.add_argument('--twitter_csv', type=str, default='data/all_twitter_responses.csv')
    parser.add_argument('--commit_csv', type=str, default='data/all_commit_responses.csv')
    parser.add_argument('--output_file', type=str, default=os.path.join(OUTPUT_DIR, 'uq_methods_complete_analysis.csv'))
    parser.add_argument('--nli_model', type=str, default='microsoft/deberta-large-mnli',
                       choices=['microsoft/deberta-large-mnli', 'cross-encoder/nli-deberta-v3-base', 'potsawee/deberta-v3-large-mnli'])
    parser.add_argument('--max_twitter_identifiers', type=int, default=None,
                       help='Limit number of Twitter identifiers to process (for testing)')
    parser.add_argument('--max_commit_identifiers', type=int, default=None,
                       help='Limit number of Commit identifiers to process (default: 50)')
    parser.add_argument('--resume', action='store_true',
                       help='Resume from last checkpoint if available')
    parser.add_argument('--skip_twitter', action='store_true',
                       help='Skip Twitter data analysis')
    parser.add_argument('--skip_commit', action='store_true',
                       help='Skip Commit data analysis')
    parser.add_argument('--checkpoint_file', type=str, default=os.path.join(OUTPUT_DIR, 'analysis_checkpoint.pkl'),
                       help='Checkpoint file for resuming interrupted analysis')

    args = parser.parse_args()

    # 创建必要的目录
    create_directories()

    # 检查是否需要恢复
    checkpoint = None
    if args.resume:
        checkpoint = load_checkpoint(args.checkpoint_file)
        if checkpoint:
            print(f"Resuming from checkpoint with {len(checkpoint.get('results', []))} existing results")
            all_results = checkpoint.get('results', [])
            processed_twitter = checkpoint.get('processed_twitter', False)
            processed_commit = checkpoint.get('processed_commit', False)
        else:
            print("No valid checkpoint found, starting from scratch")
            all_results = []
            processed_twitter = False
            processed_commit = False
    else:
        all_results = []
        processed_twitter = False
        processed_commit = False

    # 分析Twitter数据
    if not args.skip_twitter and not processed_twitter:
        try:
            print(f"Starting Twitter data analysis...")
            twitter_results = analyze_all_uq_methods(
                args.twitter_csv,
                'twitter',
                args.nli_model,
                max_identifiers=args.max_twitter_identifiers
            )
            all_results.extend(twitter_results)
            processed_twitter = True

            # 保存检查点
            checkpoint_data = {
                'results': all_results,
                'processed_twitter': processed_twitter,
                'processed_commit': processed_commit,
                'timestamp': pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            save_checkpoint(checkpoint_data, args.checkpoint_file)

            print(f"Completed Twitter analysis: {len(twitter_results)} results")
        except Exception as e:
            logger.error(f"Error analyzing Twitter data: {str(e)}")
            import traceback
            traceback.print_exc()

            # 保存中断检查点
            checkpoint_data = {
                'results': all_results,
                'processed_twitter': False,
                'processed_commit': processed_commit,
                'error': str(e),
                'timestamp': pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            save_checkpoint(checkpoint_data, args.checkpoint_file)
    elif args.skip_twitter:
        print("Skipping Twitter data analysis as requested")
    elif processed_twitter:
        print("Twitter data analysis already completed in previous run")

    # 分析Commit数据（由于数据量大，限制处理数量）
    if not args.skip_commit and not processed_commit:
        try:
            print(f"Starting Commit data analysis (limited to {args.max_commit_identifiers} identifiers)...")
            commit_results = analyze_all_uq_methods(
                args.commit_csv,
                'commit',
                args.nli_model,
                max_identifiers=args.max_commit_identifiers
            )
            all_results.extend(commit_results)
            processed_commit = True

            # 保存检查点
            checkpoint_data = {
                'results': all_results,
                'processed_twitter': processed_twitter,
                'processed_commit': processed_commit,
                'timestamp': pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            save_checkpoint(checkpoint_data, args.checkpoint_file)

            print(f"Completed Commit analysis: {len(commit_results)} results")
        except Exception as e:
            logger.error(f"Error analyzing Commit data: {str(e)}")
            import traceback
            traceback.print_exc()

            # 保存中断检查点
            checkpoint_data = {
                'results': all_results,
                'processed_twitter': processed_twitter,
                'processed_commit': False,
                'error': str(e),
                'timestamp': pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            save_checkpoint(checkpoint_data, args.checkpoint_file)
    elif args.skip_commit:
        print("Skipping Commit data analysis as requested")
    elif processed_commit:
        print("Commit data analysis already completed in previous run")
    
    # 保存结果和生成报告
    if all_results:
        results_df = pd.DataFrame(all_results)
        results_df.to_csv(args.output_file, index=False, encoding='utf-8-sig')
        print(f"\nAll results saved to {args.output_file}")
        print(f"Total results: {len(results_df)}")

        # 创建汇总表格
        create_summary_tables(results_df)

        # 生成数据透视表
        print("\n=== Generating Pivot Tables ===")
        pivot_files = generate_pivot_tables(results_df, OUTPUT_DIR)
        for table_type, file_path in pivot_files.items():
            print(f"Generated {table_type} pivot table: {file_path}")

        # 生成综合报告
        print("\n=== Generating Comprehensive Report ===")
        report_generator = ReportGenerator(OUTPUT_DIR)

        # 准备缓存统计
        cache_stats = {
            'nli_entries': len(NLI_CSV_CACHE),
            'hit_rate': 'N/A'  # 可以在未来版本中计算实际命中率
        }

        # 生成报告
        report_files = report_generator.generate_comprehensive_report(
            results_df,
            validation_report=None,  # 可以传入验证报告
            cache_stats=cache_stats
        )

        for report_type, file_path in report_files.items():
            print(f"Generated {report_type} report: {file_path}")

        # 显示基本统计信息
        print("\n=== Basic Statistics ===")
        print("Results by data type and method:")
        print(results_df.groupby(['data_type', 'uq_method']).size())

        print("\nSample sizes tested:")
        print(results_df.groupby(['data_type', 'sample_size']).size())

        print("\nMethod types:")
        results_df['method_type'] = results_df['uq_method'].apply(
            lambda x: 'NLI' if 'nli' in x else ('Jaccard' if 'jaccard' in x else 'NumSets')
        )
        results_df['method_base'] = results_df['uq_method'].apply(
            lambda x: x.replace('_nli', '').replace('_jaccard', '') if x != 'num_sets' else 'num_sets'
        )
        print(results_df.groupby(['data_type', 'method_type', 'method_base']).size())

        # 显示Twitter validation准确率统计
        if 'validation_accuracy' in results_df.columns:
            twitter_results = results_df[results_df['data_type'] == 'twitter']
            if not twitter_results.empty:
                print("\n=== Twitter Validation Accuracy Statistics ===")
                print("Average validation accuracy by method:")
                avg_accuracy = twitter_results.groupby('uq_method')['validation_accuracy'].mean().sort_values(ascending=False)
                print(avg_accuracy.round(4))

                print("\nValidation accuracy by prompt type:")
                prompt_accuracy = twitter_results.groupby(['prompt_type', 'uq_method'])['validation_accuracy'].mean()
                print(prompt_accuracy.round(4))

        # 显示缓存统计
        print(f"\nNLI CSV cache: {len(NLI_CSV_CACHE)} entries")

        # 最终保存CSV缓存
        print("\n=== Saving final CSV cache ===")
        save_nli_csv_cache()
        print("CSV cache saved successfully")

        # 显示生成的文件摘要
        print("\n=== Generated Files Summary ===")
        print(f"Main results: {args.output_file}")
        print(f"Pivot tables: {len(pivot_files)} files")
        print(f"Reports: {len(report_files)} files")
        print(f"All files saved to: {OUTPUT_DIR}")

    else:
        print("No results generated")

if __name__ == "__main__":
    main()
