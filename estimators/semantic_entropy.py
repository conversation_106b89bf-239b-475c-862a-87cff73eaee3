"""
Semantic Entropy implementation based on "Detecting hallucinations in large language models using semantic entropy"
This module implements NLI-based clustering for semantic uncertainty estimation.
"""

import numpy as np
from typing import List, Dict
import logging
from dataclasses import dataclass
import re

# Import our unified client
from src.openai_client import OpenAICompatibleClient, extract_json_from_response

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class NLIResult:
    """Result of Natural Language Inference between two texts."""
    entailment: float
    contradiction: float
    neutral: float
    
    def __post_init__(self):
        # Ensure probabilities sum to 1
        total = self.entailment + self.contradiction + self.neutral
        if total > 0 and abs(total - 1.0) > 1e-6:
            self.entailment /= total
            self.contradiction /= total
            self.neutral /= total
        elif total == 0:
            # If all values are 0, set to uniform distribution
            self.entailment = 1.0 / 3.0
            self.contradiction = 1.0 / 3.0
            self.neutral = 1.0 / 3.0


@dataclass
class SemanticCluster:
    """Represents a cluster of semantically equivalent responses."""
    cluster_id: int
    responses: List[str]
    representative: str
    size: int = 0


class SemanticEntropyCalculator:
    """
    Implements semantic entropy calculation using NLI-based clustering.
    
    This class implements the method from "Detecting hallucinations in large language 
    models using semantic entropy" paper, which uses NLI to cluster semantically 
    equivalent responses and then calculates entropy based on cluster membership.
    """
    
    def __init__(self, nli_client=None, entailment_threshold: float = 0.5):
        """
        Initialize the semantic entropy calculator.
        
        Args:
            nli_client: OpenAI-compatible client for NLI tasks
            entailment_threshold: Threshold for considering two responses as semantically equivalent
        """
        self.nli_client = nli_client
        self.entailment_threshold = entailment_threshold
        self.clusters: List[SemanticCluster] = []
        
    def calculate_semantic_entropy(self, responses: List[str]) -> float:
        """
        Calculate semantic entropy for a list of responses.
        
        Args:
            responses: List of generated responses from the LLM
            
        Returns:
            Semantic entropy value (higher = more uncertain/less consistent)
        """
        if len(responses) == 0:
            return 0.0
            
        if len(responses) == 1:
            return 0.0
            
        # Step 1: Cluster responses based on NLI relationships
        clusters = self._cluster_responses(responses)
        self.clusters = clusters
        
        # Step 2: Calculate entropy based on cluster distribution
        entropy = self._calculate_cluster_entropy(clusters)
        
        return entropy
    
    def _cluster_responses(self, responses: List[str]) -> List[SemanticCluster]:
        """
        Cluster responses based on NLI relationships.
        
        This method implements the NLI-based clustering approach where:
        - Two responses are in the same cluster if they have high mutual entailment
        - Contradiction scores are used to determine when responses should be separated
        
        Args:
            responses: List of responses to cluster
            
        Returns:
            List of SemanticCluster objects
        """
        if not responses:
            return []
            
        n = len(responses)
        clusters = []
        assigned = set()
        
        # Build similarity matrix based on NLI
        similarity_matrix = self._build_nli_matrix(responses)
        
        # Group responses based on NLI relationships
        cluster_id = 0
        for i in range(n):
            if i in assigned:
                continue
                
            # Start new cluster with this response
            cluster_responses = [responses[i]]
            assigned.add(i)
            
            # Find all responses that are semantically equivalent
            for j in range(n):
                if j in assigned or j == i:
                    continue
                    
                # Check if responses i and j are semantically equivalent
                if self._are_semantically_equivalent(
                    similarity_matrix[i][j], 
                    similarity_matrix[j][i]
                ):
                    cluster_responses.append(responses[j])
                    assigned.add(j)
            
            # Create cluster
            cluster = SemanticCluster(
                cluster_id=cluster_id,
                responses=cluster_responses,
                representative=cluster_responses[0],
                size=len(cluster_responses)
            )
            clusters.append(cluster)
            cluster_id += 1
            
        return clusters
    
    def _build_nli_matrix(self, responses: List[str]) -> List[List[NLIResult]]:
        """
        Build NLI similarity matrix for all response pairs.
        
        Args:
            responses: List of responses
            
        Returns:
            NxN matrix of NLIResult objects
        """
        n = len(responses)
        matrix = [[NLIResult(0.0, 0.0, 0.0) for _ in range(n)] for _ in range(n)]
        
        for i in range(n):
            for j in range(n):
                if i == j:
                    # Self-comparison is full entailment
                    matrix[i][j] = NLIResult(1.0, 0.0, 0.0)
                else:
                    # Get NLI scores for responses[i] -> responses[j]
                    nli_result = self._get_nli_scores(responses[i], responses[j])
                    matrix[i][j] = nli_result
                    
        return matrix
    
    def _get_nli_scores(self, premise: str, hypothesis: str) -> NLIResult:
        """
        Get NLI scores for premise and hypothesis using OpenAI-compatible client.
        
        If nli_client is provided, use it for NLI scoring. Otherwise, fallback to
        basic similarity-based heuristics.
        
        Args:
            premise: First text
            hypothesis: Second text
            
        Returns:
            NLIResult with entailment, contradiction, and neutral scores
        """
        # Import prompts here to avoid circular imports
        from prompts import PROMPTS
        
        # Use provided NLI client if available
        if hasattr(self, 'nli_client') and self.nli_client is not None:
            try:
                nli_prompt = PROMPTS['nli_classification'].format(
                    premise=premise,
                    hypothesis=hypothesis
                )
                
                messages = [{"role": "user", "content": nli_prompt}]
                response = self.nli_client.chat_completion(
                    messages=messages,
                    max_tokens=150,
                    temperature=0.1
                )
                
                content = response["choices"][0]["message"]["content"]
                
                # Extract JSON from response
                import re
                json_match = re.search(r'\{[^}]*\}', content, re.DOTALL)
                if json_match:
                    import json
                    scores = json.loads(json_match.group())
                    return NLIResult(
                        entailment=float(scores.get('entailment', 0.33)),
                        contradiction=float(scores.get('contradiction', 0.33)),
                        neutral=float(scores.get('neutral', 0.34))
                    )
                
                # Fallback to pattern matching
                content_lower = content.lower()
                if 'entailment' in content_lower:
                    return NLIResult(0.8, 0.1, 0.1)
                elif 'contradiction' in content_lower:
                    return NLIResult(0.1, 0.8, 0.1)
                else:
                    return NLIResult(0.3, 0.3, 0.4)
                    
            except Exception as e:
                logger.warning(f"NLI client failed, using fallback: {e}")
        
        # Fallback to basic similarity-based heuristics
        premise_clean = self._normalize_text(premise)
        hypothesis_clean = self._normalize_text(hypothesis)
        
        # Simple similarity measure as fallback
        similarity = self._text_similarity(premise_clean, hypothesis_clean)
        
        # Convert similarity to reasonable NLI scores
        if similarity > 0.8:
            return NLIResult(0.8, 0.1, 0.1)
        elif similarity < 0.3:
            return NLIResult(0.1, 0.7, 0.2)
        else:
            return NLIResult(0.3, 0.3, 0.4)
    
    def _are_semantically_equivalent(self, nli_ab: NLIResult, nli_ba: NLIResult) -> bool:
        """
        Determine if two responses are semantically equivalent based on NLI scores.
        
        Two responses are considered equivalent if:
        - Both have high entailment scores (mutual entailment)
        - Both have low contradiction scores
        
        Args:
            nli_ab: NLI result for response A -> response B
            nli_ba: NLI result for response B -> response A
            
        Returns:
            True if responses are semantically equivalent
        """
        # Check mutual entailment
        mutual_entailment = min(nli_ab.entailment, nli_ba.entailment)
        
        # Check low contradiction
        max_contradiction = max(nli_ab.contradiction, nli_ba.contradiction)
        
        # Decision criteria from the paper
        return (mutual_entailment > self.entailment_threshold and 
                max_contradiction < (1.0 - self.entailment_threshold))
    
    def _calculate_cluster_entropy(self, clusters: List[SemanticCluster]) -> float:
        """
        Calculate entropy based on cluster distribution.
        
        Args:
            clusters: List of semantic clusters
            
        Returns:
            Shannon entropy of cluster distribution
        """
        if not clusters:
            return 0.0
            
        total_responses = sum(cluster.size for cluster in clusters)
        if total_responses == 0:
            return 0.0
            
        # Calculate probabilities for each cluster
        probabilities = [cluster.size / total_responses for cluster in clusters]
        
        # Calculate Shannon entropy
        entropy = 0.0
        for p in probabilities:
            if p > 0:
                entropy -= p * np.log2(p)
                
        return entropy
    
    def _normalize_text(self, text: str) -> str:
        """Normalize text for comparison."""
        if not text:
            return ""
            
        # Convert to lowercase and remove extra whitespace
        text = text.lower().strip()
        text = re.sub(r'\s+', ' ', text)
        
        # Remove common punctuation for basic comparison
        text = re.sub(r'[^\w\s]', '', text)
        
        return text
    
    def _text_similarity(self, text1: str, text2: str) -> float:
        """
        Simple text similarity measure as placeholder for NLI.
        
        This is a basic implementation. In practice, you would use:
        - Sentence embeddings (e.g., Sentence-BERT)
        - NLI model scores
        - More sophisticated similarity measures
        """
        if not text1 or not text2:
            return 0.0
            
        # Simple Jaccard similarity on words
        words1 = set(text1.split())
        words2 = set(text2.split())
        
        if not words1 and not words2:
            return 1.0
            
        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))
        
        return intersection / union if union > 0 else 0.0
    
    def get_cluster_info(self) -> Dict:
        """Get information about the current clusters."""
        if not self.clusters:
            return {"clusters": [], "total_clusters": 0}
            
        return {
            "clusters": [
                {
                    "id": cluster.cluster_id,
                    "size": cluster.size,
                    "representative": cluster.representative,
                    "responses": cluster.responses
                }
                for cluster in self.clusters
            ],
            "total_clusters": len(self.clusters)
        }


class AdvancedNLIModel:
    """
    Advanced NLI model wrapper that can use actual pre-trained models.
    
    This class provides a wrapper for using actual NLI models like:
    - Hugging Face transformers (e.g., roberta-large-mnli)
    - OpenAI API with NLI prompts
    - Custom fine-tuned models
    """
    
    def __init__(self, model_name: str = "microsoft/deberta-v3-base-mnli"):
        """
        Initialize NLI model.
        
        Args:
            model_name: Name of the pre-trained NLI model
        """
        self.model_name = model_name
        self.model = None
        self.tokenizer = None
        
    def load_model(self):
        """Load the actual NLI model."""
        try:
            from transformers import AutoTokenizer, AutoModelForSequenceClassification
            import torch
            
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
            self.model = AutoModelForSequenceClassification.from_pretrained(self.model_name)
            
            if torch.cuda.is_available():
                self.model = self.model.cuda()
                
            logger.info(f"Loaded NLI model: {self.model_name}")
            
        except ImportError as e:
            logger.warning(f"Could not load transformers: {e}")
            logger.warning("Falling back to basic similarity")
            self.model = None
    
    def predict(self, premise: str, hypothesis: str) -> NLIResult:
        """
        Get NLI prediction using actual model.
        
        Args:
            premise: First text
            hypothesis: Second text
            
        Returns:
            NLIResult with probabilities
        """
        if self.model is None:
            # Fallback to basic similarity
            from .semantic_entropy import SemanticEntropyCalculator
            calculator = SemanticEntropyCalculator()
            similarity = calculator._text_similarity(premise, hypothesis)
            
            if similarity > 0.8:
                return NLIResult(0.9, 0.05, 0.05)
            elif similarity < 0.3:
                return NLIResult(0.05, 0.85, 0.1)
            else:
                return NLIResult(0.2, 0.2, 0.6)
        
        # Actual model prediction
        try:
            import torch
            import torch.nn.functional as F
            
            inputs = self.tokenizer(
                premise, 
                hypothesis, 
                return_tensors="pt", 
                truncation=True, 
                max_length=512
            )
            
            if torch.cuda.is_available():
                inputs = {k: v.cuda() for k, v in inputs.items()}
            
            with torch.no_grad():
                outputs = self.model(**inputs)
                logits = outputs.logits
                probs = F.softmax(logits, dim=-1)
                
            # Map to NLIResult (assuming MNLI order: contradiction, neutral, entailment)
            contradiction = probs[0][0].item()
            neutral = probs[0][1].item()
            entailment = probs[0][2].item()
            
            return NLIResult(entailment, contradiction, neutral)
            
        except Exception as e:
            logger.error(f"Error in NLI prediction: {e}")
            return NLIResult(0.33, 0.33, 0.34)  # Uniform fallback


def example_usage():
    """Example usage of the semantic entropy calculator."""
    
    # Sample responses that demonstrate semantic variation
    responses = [
        "The capital of France is Paris.",
        "Paris is the capital city of France.",
        "France's capital is Paris.",
        "The capital of France is Lyon.",
        "Lyon is the capital of France.",
        "The main city in France is Paris."
    ]
    
    # Initialize calculator with basic similarity (replace with actual NLI model)
    calculator = SemanticEntropyCalculator()
    
    # Calculate semantic entropy
    entropy = calculator.calculate_semantic_entropy(responses)
    
    print(f"Semantic Entropy: {entropy:.4f}")
    print(f"Number of clusters: {len(calculator.clusters)}")
    
    # Show cluster information
    cluster_info = calculator.get_cluster_info()
    for cluster in cluster_info["clusters"]:
        print(f"\nCluster {cluster['id']} (size={cluster['size']}):")
        print(f"Representative: {cluster['representative']}")
        for response in cluster['responses']:
            print(f"  - {response}")


if __name__ == "__main__":
    example_usage()