# LLM不确定性分析系统配置文件

# 模型配置
model:
  name: "qwen3-32b"
  base_url: "https://dashscope.aliyuncs.com/compatible-mode/v1"
  api_key_env: "DASHSCOPE_API_KEY"
  temperature: 0.7
  top_p: 0.95
  max_tokens: null
  enable_thinking: true
  enable_logprobs: false
  top_logprobs: 5
  stream: true

# 输出配置
output:
  format: "mongodb"  # mongodb, csv, json
  # MongoDB设置
  mongo:
    host: "localhost"
    port: 27017
    database: "LLM-UQ"
    collection: "response_collections"
    test_collection: "test_response"
  # 文件输出设置
  file:
    output_dir: "output"
    filename_prefix: "llm_responses"

# 日志配置
logging:
  level: "INFO"
  file: "llm_response_generator.log"

# 任务配置
tasks:
  sentiment_analysis:
    enabled: true
    name: "sentiment_analysis"
    task_category: "sentiment_analysis"
    dataset_source: "twitter_sentiment"
    data_file: "sampled_semeval.csv"
    prompt_dir: "prompts/1_sentiment_analysis"
    num_prompts: 10
    sample_prompts: 5  # 从10个中随机选择5个
    attempts_per_prompt: 8  # 每个prompt重复8次
    template_variable: "tweet"  # prompt中的占位符
    id_field: "id"
    text_field: "text"
    label_field: "label"
  
  explorative_coding:
    enabled: true
    name: "explorative_coding"
    task_category: "open_explorative_coding"
    dataset_source: "pytorch_commits"
    data_file: "sampled_commits.csv"
    prompt_dir: "prompts/2_explorative_coding_commits"
    num_prompts: 10
    sample_prompts: 5  # 从10个中随机选择5个
    attempts_per_prompt: 8  # 每个prompt重复8次
    template_variables:
      repo_name: "pytorch/pytorch"
      message: "message"
    id_field: "sha"
    text_field: "message"
    label_field: null

# 系统配置
system:
  max_concurrent_requests: 10
  max_retries: 3
  retry_delay: 1.0
  enable_resume: true
  test_mode_sample_size: 1
