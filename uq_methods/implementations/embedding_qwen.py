from typing import List, Dict, Any, Optional


import numpy as np

from uq_methods.base import BaseUQMethod
from core.embedding_cache import get_embedding_encoder





class EmbeddingQwenUQ(BaseUQMethod):
    """
    Embedding-based UQ using Qwen/Qwen3-Embedding-0.6B.
    - Uncertainty metrics (all are 1 - cosine similarity):
      * avg_pairwise_distance: among responses
      * avg_distance_to_mean: to mean vector (unit)
      * avg_distance_to_reference: to reference text (if provided)
    - uncertainty_score priority: reference distance if available, else mean distance

    """

    def __init__(
        self,
        reference_text: Optional[str] = None,
    ) -> None:
        self.model_name = "Qwen/Qwen3-Embedding-0.6B"
        self._model = None
        self.reference_text = reference_text

    def set_reference_text(self, text: Optional[str]):
        self.reference_text = text

    def _get_model(self):
        # Deprecated: use shared cached encoder instead
        return None

    def _encode_embedding(self, text: str) -> np.ndarray:
        try:
            encoder = get_embedding_encoder(self.model_name)
            vec = encoder.encode_one(text, normalize=True)
        except Exception as e:
            raise RuntimeError(f"Qwen encode failed: {e}")
        return vec

    def compute_uncertainty(self, responses: List[str]) -> Dict[str, Any]:
        if len(responses) < 2:
            return {"uncertainty_score": None, "error": "<2 responses"}
        # embeddings
        X_list: List[np.ndarray] = []
        for r in responses:
            v = self._encode_embedding(r)
            if v is not None:
                X_list.append(v)
        if len(X_list) < 2:
            return {"uncertainty_score": None, "error": "<2 embeddings"}
        X = np.stack(X_list, axis=0)
        n = X.shape[0]
        # pairwise
        sim_mat = X @ X.T
        avg_sim = (sim_mat.sum() - n) / (n * (n - 1)) if n > 1 else 1.0
        avg_pairwise_distance = float(1.0 - avg_sim)
        # mean distance
        mean_vec = X.mean(axis=0)
        mean_unit = mean_vec / (np.linalg.norm(mean_vec) + 1e-12)
        sims_to_mean = X @ mean_unit
        avg_distance_to_mean = float(1.0 - float(np.mean(sims_to_mean)))
        # reference distance
        avg_distance_to_ref = None
        if self.reference_text:
            try:
                ref_vec = self._encode_embedding(self.reference_text)
                ref_unit = ref_vec / (np.linalg.norm(ref_vec) + 1e-12)
                sims_to_ref = X @ ref_unit
                avg_distance_to_ref = float(1.0 - float(np.mean(sims_to_ref)))
            except Exception:
                avg_distance_to_ref = None
        # choose main score
        uq_value = avg_distance_to_mean if avg_distance_to_ref is None else avg_distance_to_ref
        return {
            "uncertainty_score": uq_value,
            "avg_pairwise_distance": avg_pairwise_distance,
            "avg_distance_to_mean": avg_distance_to_mean,
            "avg_distance_to_reference": avg_distance_to_ref,
            "num_responses": len(responses),
            "method": "Embedding_Qwen",
        }

    def get_required_samples(self) -> int:
        return 2

    def get_method_name(self) -> str:
        return "Embedding_Qwen"

