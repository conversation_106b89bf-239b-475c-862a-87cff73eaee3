"""
NLI Semantic Entropy method using <PERSON><PERSON>'s NLI reasoning capabilities
"""
import asyncio
import numpy as np
from typing import List, Dict, Any
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.feature_extraction.text import TfidfVectorizer
from uq_methods.base import UQMethod


class NLISemanticEntropyMethod(UQMethod):
    """
    NLI-specific semantic entropy method leveraging <PERSON>wen's NLI reasoning
    """
    
    def __init__(self, name: str, similarity_threshold: float = 0.85, n_samples: int = 5):
        super().__init__(name)
        self.description = "NLI semantic entropy using premise-hypothesis uncertainty quantification"
        self.similarity_threshold = similarity_threshold
        self.n_samples = n_samples
        self.vectorizer = TfidfVectorizer(max_features=500, stop_words='english')
    
    async def evaluate_uncertainty(
        self,
        prompts: List[str],
        llm_client,
        sample: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Evaluate uncertainty for NLI tasks using <PERSON><PERSON>'s NLI reasoning
        
        Args:
            prompts: List of prompts (should contain premise and hypothesis)
            llm_client: Qwen client with NLI capabilities
            sample: Original sample with premise/hypothesis data
            
        Returns:
            Dictionary containing NLI uncertainty metrics
        """
        if not sample or 'premise' not in sample or 'hypothesis' not in sample:
            return {
                'uncertainty_score': 1.0,
                'nli_results': [],
                'confidence': 0.0,
                'label_distribution': {},
                'metadata': {'error': 'Missing premise/hypothesis in sample'}
            }
        
        premise = sample['premise']
        hypothesis = sample['hypothesis']
        expected_label = sample.get('expected_label', 'UNKNOWN')
        
        try:
            # Use Qwen's NLI reasoning for each prompt variation
            nli_results = []
            for prompt in prompts:
                # Generate NLI reasoning for this prompt variation
                nli_result = await llm_client.generate_nli_reasoning(premise, hypothesis)
                nli_results.append(nli_result)
            
            # Calculate uncertainty based on NLI label distribution
            uncertainty_metrics = self._calculate_nli_uncertainty(nli_results)
            
            return {
                'uncertainty_score': uncertainty_metrics['uncertainty_score'],
                'nli_results': nli_results,
                'confidence': uncertainty_metrics['confidence'],
                'label_distribution': uncertainty_metrics['label_distribution'],
                'semantic_entropy': uncertainty_metrics['semantic_entropy'],
                'premise': premise,
                'hypothesis': hypothesis,
                'expected_label': expected_label,
                'accuracy': self._calculate_accuracy(nli_results, expected_label),
                'metadata': {
                    'method': self.name,
                    'prompt_count': len(prompts),
                    'samples_generated': len(nli_results),
                    'similarity_threshold': self.similarity_threshold
                }
            }
            
        except Exception as e:
            return {
                'uncertainty_score': 1.0,
                'nli_results': [],
                'confidence': 0.0,
                'premise': premise,
                'hypothesis': hypothesis,
                'error': str(e),
                'metadata': {'method': self.name}
            }
    
    def _calculate_nli_uncertainty(self, nli_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Calculate uncertainty from NLI results
        
        Args:
            nli_results: List of NLI reasoning results
            
        Returns:
            Dictionary with uncertainty metrics
        """
        if not nli_results:
            return {
                'uncertainty_score': 1.0,
                'confidence': 0.0,
                'label_distribution': {},
                'semantic_entropy': 1.0
            }
        
        # Extract labels and justifications
        labels = [result['label'] for result in nli_results]
        justifications = [result['explanation'] for result in nli_results]
        
        # Calculate label distribution
        label_counts = {}
        for label in labels:
            label_counts[label] = label_counts.get(label, 0) + 1
        
        # Calculate semantic entropy based on explanations
        semantic_entropy = self._calculate_explanation_entropy(justifications)
        
        # Calculate uncertainty based on label distribution and explanation diversity
        total_samples = len(labels)
        label_probs = [count / total_samples for count in label_counts.values()]
        label_entropy = -sum(p * np.log(p) for p in label_probs if p > 0)
        max_label_entropy = np.log(len(label_counts)) if len(label_counts) > 1 else 0.0
        normalized_label_entropy = label_entropy / max_label_entropy if max_label_entropy > 0 else 0.0
        
        # Combine metrics for final uncertainty score
        uncertainty_score = min(1.0, (normalized_label_entropy + semantic_entropy) / 2.0)
        confidence = 1.0 - uncertainty_score
        
        return {
            'uncertainty_score': uncertainty_score,
            'confidence': confidence,
            'label_distribution': label_counts,
            'semantic_entropy': semantic_entropy,
            'dominant_label': max(label_counts, key=label_counts.get) if label_counts else "UNKNOWN"
        }
    
    def _calculate_explanation_entropy(self, justifications: List[str]) -> float:
        """
        Calculate semantic entropy based on justification diversity
        
        Args:
            justifications: List of NLI explanations
            
        Returns:
            Normalized entropy score [0, 1]
        """
        if len(justifications) < 2:
            return 0.0
        
        try:
            # Preprocess justifications
            clean_justifications = [self._preprocess_text(just) for just in justifications if just]
            if len(set(clean_justifications)) == 1:
                return 0.0
            
            # Calculate TF-IDF vectors
            tfidf_matrix = self.vectorizer.fit_transform(clean_justifications)
            similarities = cosine_similarity(tfidf_matrix)
            
            # Calculate average pairwise similarity
            n = len(justifications)
            avg_similarity = (similarities.sum() - n) / (n * (n - 1)) if n > 1 else 1.0
            
            # Convert similarity to diversity measure
            diversity = 1.0 - avg_similarity
            return max(0.0, min(1.0, diversity))
            
        except Exception:
            return 0.5
    
    def _preprocess_text(self, text: str) -> str:
        """Preprocess text for analysis"""
        if not text:
            return ""
        
        text = text.lower().strip()
        text = text.replace("the premise and hypothesis are", "")
        text = text.replace("this is because", "")
        text = text.replace("therefore", "")
        return text.strip()
    
    def _calculate_accuracy(self, nli_results: List[Dict[str, Any]], expected_label: str) -> float:
        """Calculate accuracy against expected label"""
        if not nli_results or expected_label == "UNKNOWN":
            return 0.0
        
        correct = sum(1 for result in nli_results if result['label'] == expected_label)
        return correct / len(nli_results) if nli_results else 0.0
    
    def get_info(self) -> Dict[str, Any]:
        """Get method information"""
        return {
            'name': self.name,
            'description': self.description,
            'similarity_threshold': self.similarity_threshold,
            'n_samples': self.n_samples,
            'features': [
                'NLI label distribution analysis',
                'Explanation semantic diversity',
                'Qwen NLI reasoning integration',
                'Stream processing support'
            ]
        }