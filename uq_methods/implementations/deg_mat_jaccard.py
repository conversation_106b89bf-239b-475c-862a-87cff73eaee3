# import numpy as np
# import logging
# from typing import List, Dict, Any
# from uq_methods.base import BaseUQMethod
# from core.similarity_cache import jaccard_similarity_cached

# log = logging.getLogger(__name__)


# class DegMatJaccardUQ(BaseUQMethod):
#     """
#     Degree Matrix method using Jaccard Score for similarity computation.
#     Estimates the sequence-level uncertainty of a language model following the method of
#     "The Degree Matrix" as provided in the paper https://arxiv.org/abs/2305.19187.
    
#     Elements on diagonal of matrix D are sums of similarities between the particular number
#     (position in matrix) and other answers. Thus, it is an average pairwise distance
#     (lower values indicated smaller distance between answers which means greater uncertainty).
#     """

#     def __init__(self, verbose: bool = False):
#         """
#         Initialize Degree Matrix with Jaccard score similarity.
        
#         Parameters:
#             verbose (bool): Whether to print debug information
#         """
#         self.verbose = verbose

#     def _compute_jaccard_similarity(self, text1: str, text2: str) -> float:
#         """Compute Jaccard similarity between two texts (cached)."""
#         return jaccard_similarity_cached(text1, text2)

#     def _compute_similarity_matrix(self, responses: List[str]) -> np.ndarray:
#         """Compute similarity matrix using Jaccard scores."""
#         n = len(responses)
#         W = np.zeros((n, n))
        
#         for i in range(n):
#             for j in range(n):
#                 if i == j:
#                     W[i, j] = 1.0
#                 else:
#                     W[i, j] = self._compute_jaccard_similarity(responses[i], responses[j])
        
#         W = (W + np.transpose(W)) / 2
#         return W

#     def _compute_deg_mat(self, responses: List[str]) -> float:
#         """Compute the Degree Matrix uncertainty score."""
#         if len(responses) < 2:
#             return 0.0
            
#         W = self._compute_similarity_matrix(responses)
#         D = np.diag(W.sum(axis=1))
        
#         # Compute uncertainty score: trace(len(answers) - D) / (len(answers) ** 2)
#         n = len(responses)
#         uncertainty_score = np.trace(n - D) / (n ** 2)
        
#         return uncertainty_score

#     def compute_uncertainty(self, responses: List[str]) -> Dict[str, Any]:
#         """Compute uncertainty using Degree Matrix with Jaccard similarity."""
#         if len(responses) < 2:
#             return {
#                 "uncertainty_score": 0.0,
#                 "error": "Need at least 2 responses",
#                 "method": "DegMat_Jaccard"
#             }
        
#         try:
#             uncertainty_score = self._compute_deg_mat(responses)
#             similarity_matrix = self._compute_similarity_matrix(responses)
#             mean_similarity = np.mean(similarity_matrix[np.triu_indices_from(similarity_matrix, k=1)])
            
#             if self.verbose:
#                 log.debug(f"Generated responses: {responses}")
#                 log.debug(f"Uncertainty score: {uncertainty_score}")
#                 log.debug(f"Mean similarity: {mean_similarity}")
            
#             return {
#                 "uncertainty_score": uncertainty_score,
#                 "mean_similarity": mean_similarity,
#                 "num_responses": len(responses),
#                 "method": "DegMat_Jaccard",
#                 "similarity_matrix": similarity_matrix.tolist(),
#                 "metadata": {
#                     "method": "DegMat_Jaccard",
#                     "similarity_score": "Jaccard_score",
#                     "verbose": self.verbose
#                 }
#             }
            
#         except Exception as e:
#             log.error(f"Error computing DegMat Jaccard uncertainty: {str(e)}")
#             return {
#                 "uncertainty_score": 1.0,
#                 "error": str(e),
#                 "method": "DegMat_Jaccard"
#             }

#     def get_required_samples(self) -> int:
#         return 5

#     def get_method_name(self) -> str:
#         return "DegMat_Jaccard" 