import sys
import os
import numpy as np
import networkx as nx
from typing import List, Dict, Any

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from uq_methods.base import BaseUQMethod

class NumSetsUQ(BaseUQMethod):
    """基于NLI的NumSets不确定性度量方法 - 使用统一NLI系统"""
    
    def __init__(self, model_name: str = "microsoft/deberta-large-mnli", use_unified_nli: bool = True):
        self.model_name = model_name
        self.use_unified_nli = use_unified_nli
        
        # 默认使用统一NLI系统
        if use_unified_nli:
            try:
                # 导入统一的NLI计算器
                from core.nli_calculator import CachedNLICalculator
                self.nli_calculator = CachedNLICalculator(model_name, verbose=False)
            except ImportError:
                print("Warning: Cannot import unified NLI calculator, falling back to independent implementation")
                self._init_independent_nli()
        else:
            # 向后兼容的独立实现
            self._init_independent_nli()
    
    def _init_independent_nli(self):
        """初始化独立的NLI实现（向后兼容）"""
        import torch
        from transformers import AutoTokenizer, AutoModelForSequenceClassification, DebertaV2Tokenizer
        
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        if self.model_name == "potsawee/deberta-v3-large-mnli":
            try:
                self.tokenizer = DebertaV2Tokenizer.from_pretrained(self.model_name)
            except:
                self.tokenizer = AutoTokenizer.from_pretrained(self.model_name, use_fast=False)
        else:
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
            
        self.model = AutoModelForSequenceClassification.from_pretrained(self.model_name).to(self.device)
        self.model.eval()

    def pairwise_nli(self, text1: str, text2: str) -> str:
        """对两个文本做NLI推理，返回'entailment'/'contradiction'/'neutral'"""
        if self.use_unified_nli and hasattr(self, 'nli_calculator'):
            # 使用统一的NLI系统
            nli_result = self.nli_calculator.compute_nli_scores_cached(text1, text2)
            
            # 返回概率最高的标签
            scores = {
                'entailment': nli_result.entailment,
                'neutral': nli_result.neutral,
                'contradiction': nli_result.contradiction
            }
            return max(scores, key=scores.get)
        else:
            # 使用独立实现（向后兼容）
            return self._independent_pairwise_nli(text1, text2)
    
    def _independent_pairwise_nli(self, text1: str, text2: str) -> str:
        """独立的NLI推理实现（向后兼容）"""
        import torch
        
        inputs = self.tokenizer(
            text1, text2, 
            return_tensors="pt", 
            truncation=True, 
            max_length=256
        ).to(self.device)
        
        with torch.no_grad():
            logits = self.model(**inputs).logits
            probs = torch.softmax(logits, dim=-1).cpu().numpy()[0]
        
        # Label mapping: 0=contradiction, 1=neutral, 2=entailment
        labels = ["contradiction", "neutral", "entailment"]
        predicted_label = labels[np.argmax(probs)]
        return predicted_label

    def compute_uncertainty(self, responses: List[str]) -> Dict[str, Any]:
        """主流程：两两NLI+连通分量+集合熵"""
        n = len(responses)
        if n < 2:
            return {"error": "Need at least 2 responses"}
            
        # 构建无向图
        G = nx.Graph()
        G.add_nodes_from(range(n))
        nli_results = {}
        
        # 计算所有两两NLI关系
        for i in range(n):
            for j in range(n):
                if i == j:
                    continue
                label_ij = self.pairwise_nli(responses[i], responses[j])
                nli_results[(i, j)] = label_ij
                
        # 连边：只有(i->j)和(j->i)都为'entailment'才连
        for i in range(n):
            for j in range(i+1, n):
                if nli_results[(i, j)] == "entailment" and nli_results[(j, i)] == "entailment":
                    G.add_edge(i, j)
                    
        # 连通分量分组
        components = list(nx.connected_components(G))
        set_sizes = [len(c) for c in components]
        total = sum(set_sizes)
        probs = [s/total for s in set_sizes]
        
        # 计算集合熵
        entropy = -sum(p * np.log(p) for p in probs if p > 0)
        
        # 计算平均相似度（基于entailment分数）
        if self.use_unified_nli and hasattr(self, 'nli_calculator'):
            entailment_scores = []
            for i in range(n):
                for j in range(i+1, n):
                    nli_result = self.nli_calculator.compute_nli_scores_cached(responses[i], responses[j])
                    entailment_scores.append(nli_result.entailment)
            mean_similarity = float(np.mean(entailment_scores)) if entailment_scores else 0.0
        else:
            mean_similarity = 0.0  # 独立实现不计算相似度分数
        
        # 结果组织
        result = {
            "uncertainty_score": entropy,
            "num_sets": len(components),
            "set_sizes": set_sizes,
            "set_entropy": entropy,
            "sets": [[responses[idx] for idx in comp] for comp in components],
            "nli_results": {f"{i}_{j}": nli_results[(i, j)] for (i, j) in nli_results},
            "mean_similarity": mean_similarity,
            "method": "num_sets",
            "nli_system": "unified" if self.use_unified_nli else "independent"
        }
        return result

    def get_required_samples(self) -> int:
        """返回所需的最小样本数"""
        return 2
