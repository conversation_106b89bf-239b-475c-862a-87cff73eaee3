"""
Semantic Entropy uncertainty quantification method
Based on measuring semantic similarity between multiple model responses
"""
import asyncio
import numpy as np
from typing import List, Dict, Any
import re
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.feature_extraction.text import TfidfVectorizer
from uq_methods.base import UQMethod


class SemanticEntropyMethod(UQMethod):
    """
    Semantic entropy method for uncertainty quantification
    Measures uncertainty based on semantic diversity of model responses
    """
    
    def __init__(self, name: str, similarity_threshold: float = 0.85, n_samples: int = 10):
        super().__init__(name)
        self.description = "Semantic entropy based uncertainty quantification"
        self.similarity_threshold = similarity_threshold
        self.n_samples = n_samples
        self.vectorizer = TfidfVectorizer(max_features=1000, stop_words='english')
    
    async def evaluate_uncertainty(
        self,
        prompts: List[str],
        llm_client,
        sample: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Evaluate uncertainty using semantic entropy
        
        Args:
            prompts: List of prompts to evaluate
            llm_client: LLM client for generating responses
            sample: Original sample data
            
        Returns:
            Dictionary containing uncertainty metrics
        """
        if not prompts:
            return {
                'uncertainty_score': 1.0,
                'responses': [],
                'confidence': 0.0,
                'semantic_entropy': 1.0,
                'cluster_count': 0,
                'metadata': {'error': 'No prompts provided'}
            }
        
        try:
            # Generate multiple responses for each prompt
            all_responses = []
            for prompt in prompts:
                prompt_responses = []
                for i in range(self.n_samples):
                    try:
                        response = await llm_client.generate(prompt)
                        prompt_responses.append(response)
                    except Exception as e:
                        self.logger.warning(f"Failed to generate response {i}: {e}")
                        prompt_responses.append(f"Error: {str(e)}")
                all_responses.extend(prompt_responses)
            
            # Calculate semantic distances
            uncertainty_metrics = self._calculate_semantic_entropy(all_responses)
            
            return {
                'uncertainty_score': uncertainty_metrics['uncertainty_score'],
                'responses': all_responses,
                'confidence': 1.0 - uncertainty_metrics['uncertainty_score'],
                'semantic_entropy': uncertainty_metrics['semantic_entropy'],
                'cluster_count': uncertainty_metrics['cluster_count'],
                'avg_pairwise_similarity': uncertainty_metrics['avg_similarity'],
                'response_length_variance': uncertainty_metrics['length_variance'],
                'metadata': {
                    'method': self.name,
                    'prompt_count': len(prompts),
                    'responses_per_prompt': self.n_samples,
                    'total_responses': len(all_responses),
                    'similarity_threshold': self.similarity_threshold
                },
                'sample_metadata': sample.get('metadata', {}) if sample else {}
            }
            
        except Exception as e:
            return {
                'uncertainty_score': 1.0,
                'responses': [],
                'confidence': 0.0,
                'semantic_entropy': 1.0,
                'cluster_count': 0,
                'error': str(e),
                'metadata': {'method': self.name}
            }
    
    def _calculate_semantic_entropy(self, responses: List[str]) -> Dict[str, Any]:
        """
        Calculate semantic entropy from responses
        
        Args:
            responses: List of model responses
            
        Returns:
            Dictionary with entropy metrics
        """
        if len(responses) < 2:
            return {
                'uncertainty_score': 0.0,
                'semantic_entropy': 0.0,
                'cluster_count': 1,
                'avg_similarity': 1.0,
                'length_variance': 0.0
            }
        
        try:
            # Preprocess responses
            cleaned_responses = [self._preprocess_response(resp) for resp in responses if resp]
            if not cleaned_responses:
                return self._get_max_uncertainty_metrics()
            
            # Calculate TF-IDF vectors
            try:
                tfidf_matrix = self.vectorizer.fit_transform(cleaned_responses)
            except ValueError as e:
                # Handle empty vocabulary case
                return self._get_max_uncertainty_metrics()
            
            # Calculate pairwise similarities
            similarities = cosine_similarity(tfidf_matrix)
            
            # Calculate average pairwise similarity
            n = len(responses)
            avg_similarity = (similarities.sum() - n) / (n * (n - 1)) if n > 1 else 1.0
            
            # Cluster responses based on similarity threshold
            clusters = self._cluster_responses(similarities, self.similarity_threshold)
            cluster_count = len(clusters)
            
            # Calculate semantic entropy
            cluster_probs = [len(cluster) / len(responses) for cluster in clusters]
            semantic_entropy = -sum(p * np.log(p) for p in cluster_probs if p > 0)
            
            # Normalize entropy to [0, 1]
            max_entropy = np.log(len(responses))
            normalized_entropy = semantic_entropy / max_entropy if max_entropy > 0 else 0.0
            
            # Calculate response length variance
            lengths = [len(resp) for resp in cleaned_responses]
            length_variance = np.var(lengths) if lengths else 0.0
            max_length_variance = max(lengths) ** 2 if lengths else 1.0
            normalized_length_variance = length_variance / max_length_variance if max_length_variance > 0 else 0.0
            
            # Combine metrics for final uncertainty score
            uncertainty_score = min(1.0, (normalized_entropy + (1.0 - avg_similarity) + normalized_length_variance) / 3.0)
            
            return {
                'uncertainty_score': uncertainty_score,
                'semantic_entropy': normalized_entropy,
                'cluster_count': cluster_count,
                'avg_similarity': avg_similarity,
                'length_variance': normalized_length_variance,
                'clusters': clusters
            }
            
        except Exception as e:
            return self._get_max_uncertainty_metrics()
    
    def _preprocess_response(self, response: str) -> str:
        """Preprocess response text for analysis"""
        if not response:
            return ""
        
        # Convert to lowercase and remove extra whitespace
        response = response.lower().strip()
        
        # Remove common stop phrases
        stop_phrases = ["the answer is", "answer:", "i think", "in my opinion"]
        for phrase in stop_phrases:
            response = response.replace(phrase, "")
        
        # Remove extra whitespace and punctuation
        response = re.sub(r'\s+', ' ', response)
        response = re.sub(r'[^\w\s]', ' ', response)
        response = response.strip()
        
        return response
    
    def _cluster_responses(self, similarities: np.ndarray, threshold: float) -> List[List[int]]:
        """
        Cluster responses based on similarity threshold
        
        Args:
            similarities: Pairwise similarity matrix
            threshold: Similarity threshold for clustering
            
        Returns:
            List of clusters (each cluster is a list of response indices)
        """
        n = similarities.shape[0]
        clusters = []
        visited = set()
        
        for i in range(n):
            if i in visited:
                continue
                
            cluster = [i]
            visited.add(i)
            queue = [i]
            
            while queue:
                current = queue.pop(0)
                for j in range(n):
                    if j not in visited and similarities[current, j] >= threshold:
                        cluster.append(j)
                        visited.add(j)
                        queue.append(j)
            
            clusters.append(cluster)
        
        return clusters
    
    def _get_max_uncertainty_metrics(self) -> Dict[str, Any]:
        """Return metrics for maximum uncertainty case"""
        return {
            'uncertainty_score': 1.0,
            'semantic_entropy': 1.0,
            'cluster_count': 1,
            'avg_similarity': 0.0,
            'length_variance': 1.0
        }
    
    def get_info(self) -> Dict[str, Any]:
        """Get method information"""
        return {
            'name': self.name,
            'description': self.description,
            'similarity_threshold': self.similarity_threshold,
            'n_samples': self.n_samples,
            'config': {
                'vectorizer_type': 'TF-IDF',
                'max_features': 1000,
                'distance_metric': 'cosine_similarity'
            }
        }