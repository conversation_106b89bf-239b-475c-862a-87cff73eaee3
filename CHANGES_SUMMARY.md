# UQ分析系统修改摘要

## 主要修改内容

### 1. NLI缓存阶段性保存功能

#### 新增功能：
- **NLI专用缓存**：创建了独立的NLI缓存系统，与通用相似度缓存分离
- **阶段性保存**：每处理100组数据后，自动保存带时间戳的NLI缓存文件
- **缓存恢复**：启动时自动加载已有的NLI缓存

#### 相关文件：
- `cache/nli_cache.pkl` - 主NLI缓存文件
- `cache/nli_cache_YYYYMMDD_HHMMSS.pkl` - 带时间戳的阶段性保存文件

#### 新增函数：
- `load_nli_cache()` - 加载NLI缓存
- `save_nli_cache()` - 保存NLI缓存
- `save_nli_cache_with_timestamp()` - 带时间戳保存NLI缓存

### 2. 新的输出目录结构

#### 输出目录：
所有分析结果现在保存到：`data/Uq_Evaluation_20250731/`

#### 包含文件：
- `uq_methods_complete_analysis.csv` - 主要分析结果
- `twitter_uncertainty_pivot.csv` - Twitter不确定性分数汇总
- `twitter_similarity_pivot.csv` - Twitter相似度分数汇总
- `twitter_validation_accuracy_pivot.csv` - Twitter validation准确率汇总
- `commit_uncertainty_pivot.csv` - Commit不确定性分数汇总
- `commit_similarity_pivot.csv` - Commit相似度分数汇总

### 3. 目录自动创建

#### 新增功能：
- `create_directories()` - 自动创建所需的目录结构
  - `data/Uq_Evaluation_20250731/` - 结果输出目录
  - `cache/` - NLI缓存目录
  - `data/` - 通用缓存目录

## 修改的文件

### 主要文件：
1. **`analyze_all_uq_methods.py`** - 主分析脚本
   - 添加NLI缓存功能
   - 修改输出路径
   - 添加目录创建功能

2. **`run_complete_analysis.py`** - 运行脚本
   - 更新输出路径信息
   - 添加修改说明

### 新增文件：
1. **`test_directories.py`** - 目录创建测试脚本
2. **`CHANGES_SUMMARY.md`** - 此修改摘要文档

## 使用方法

### 快速测试：
```bash
python run_complete_analysis.py --quick_test
```

### 完整分析：
```bash
python run_complete_analysis.py
```

### 测试目录创建：
```bash
python test_directories.py
```

## 功能改进

### 缓存优化：
- NLI计算结果独立缓存，提高检索效率
- 阶段性保存防止意外中断导致的计算丢失
- 缓存文件带时间戳，便于追踪和恢复

### 结果管理：
- 统一的输出目录，便于结果管理
- 清晰的文件命名规范
- 自动目录创建，减少手动配置

### 错误恢复：
- 分阶段保存机制，支持中断后继续运行
- 多层缓存系统，提高计算效率
- 详细的日志记录，便于问题定位

## 注意事项

1. **存储空间**：NLI缓存文件可能会比较大，请确保有足够的磁盘空间
2. **时间戳文件**：带时间戳的缓存文件会累积，可能需要定期清理
3. **路径依赖**：所有路径都相对于项目根目录，请从正确的目录运行脚本
4. **兼容性**：保持了与原有代码的兼容性，旧的缓存文件仍然可用