sha,message,date,author
70fb673e51decdd8bf4e55244d910a8e5680d12f,"Use software approach to catch overflow ( `c10/utils/safe_numerics.h` ) on ARM devices (#89042)

Fixes #89040

Pull Request resolved: https://github.com/pytorch/pytorch/pull/89042
Approved by: https://github.com/malfet",2022-11-17T05:55:25Z,Rachel030219
cd4c32691d45c86b030e97f99d03fe4f693918e1,"Add complex32, complex64 and complex128 dtypes (#11173)

Summary:
We don't generate a corresponding Type implementations for them,
so this doesn't do anything at the moment.

We don't plan on supporting complex32 in the near future, but
it is added to reserve the name and number in case we do at
some point in the future.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/11173

Reviewed By: SsnL

Differential Revision: D9627477

Pulled By: ezyang

fbshipit-source-id: f49a44ab1c92d8a33130c249ac7b234f210a65e6",2018-09-04T02:00:47Z,<PERSON>
fea7a79e0bc622628fdcecbd9121af05d6d42249,"[special] Add ndtr (#58126)

Summary:
Reference: https://github.com/pytorch/pytorch/issues/50345

Plot:
![image](https://user-images.githubusercontent.com/19503980/117942099-54efd680-b328-11eb-8948-c3080779ce19.png)
https://colab.research.google.com/drive/1Of67A042rOImj8wrLF_fUTgoy_wVEOZS?usp=sharing

TODO:
* [x] Add docs (https://13385714-65600975-gh.circle-artifacts.com/0/docs/special.html#torch.special.ndtr)

Pull Request resolved: https://github.com/pytorch/pytorch/pull/58126

Reviewed By: anjali411

Differential Revision: D28700957

Pulled By: mruberry

fbshipit-source-id: 5b9991e97ec1e8fd01518cc9d9849108d35fe406",2021-05-31T04:08:41Z,kshitij12345
a5fbd3ef8ae5e96d7ff8584069f0f137a0cb42fb,"[vulkan][build_fix] Fix Vulkan Build; Prepacking uses new register api (#39771)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/39771

Vulkan build was not integrated with CI, it fails without this change.
There were 2 separate problems
1.  Recently added aten/src/ATen/templates/Functions.cpp missed VulkanType in header

2. Applying the new registration api, similar to xnnpack change
https://github.com/pytorch/pytorch/pull/36800

Test Plan:
`ANDROID_ABI=x86 ./scripts/build_android.sh -DUSE_VULKAN=ON` builds ok

CI integration for it is in the next PR in this stack ( https://github.com/pytorch/pytorch/pull/39767 )
job `ci/circleci: pytorch_linux_xenial_py3_clang5_android_ndk_r19c_x86_32_vulkan_build`

Differential Revision: D21975992

Pulled By: IvanKobzarev

fbshipit-source-id: b0400a9cb0ae90d7763ebeb5b8f7ee932a2148e1",2020-06-10T20:46:12Z,Ivan Kobzarev
fddfb81dd062b0770271af1537934a3062b000fa,"Add BF16 type to _autocast_to_full_precision (#67707)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/67707

https://github.com/pytorch/pytorch/pull/63939/files has added FP16 support to torchscript.

This is to add BF16 device type when doing full conversion.

Test Plan: Unit test. Also tested BF16 locally on A100 using MLP model.

Reviewed By: idning

Differential Revision: D32027152

fbshipit-source-id: b2a5ff2b22ea1e02306b0399f2b39b8493be4f45",2021-11-03T21:05:23Z,Yusuo Hu
d1f0823d2333a822e8912add1ce4ae2d551c8932,"fix clang-tidy failing all the time on random lines (#25078)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/25078

Our script is set up to only run on lines generated by diffing your branch against the base branch.

But we were using `$TRAVIS_BRANCH` to refer to the target branch, which was causing the script to diff against master, generating many spurious lines of diff output to be clang-tidy'd

Test Plan: Imported from OSS

Differential Revision: D16993054

Pulled By: suo

fbshipit-source-id: 7bffa890f6a1a2d5566ef01b9798c4eb86d8169f",2019-08-23T19:47:42Z,Michael Suo
ec611aca88939f2ddfd735e5a089e4b3d438035e,"[Pytorch Mobile] Expose _export_operator_list to python (#51312)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/51312

Follow up to D24690094 (https://github.com/pytorch/pytorch/commit/4a870f6518d3028293f48570ab57a8a4ad5c90e6) exposing the api in python. Created matching unit test.
ghstack-source-id: 120611452

Test Plan: Ran unit test

Reviewed By: dhruvbird

Differential Revision: D26112765

fbshipit-source-id: ffe3bb97de0a4f08b31719b4b47dcebd7d2fd42a",2021-02-01T20:04:12Z,Jacob Szwejbka
e5235fb62cc0708e364054903eb3f4ab59866db9,"Convert GuardOnDataDependentSymNode into graph break (#93373)

Extracted from https://github.com/pytorch/pytorch/pull/93150 because
I need it earlier in trunk.

Signed-off-by: Edward Z. Yang <<EMAIL>>

Pull Request resolved: https://github.com/pytorch/pytorch/pull/93373
Approved by: https://github.com/Skylion007",2023-01-31T16:03:33Z,Edward Z. Yang
a6aa336cc2e75ae8d3dc29084d6d3e256501ae9e,"[quant][graph] Fix bug in replaceConvolutionWithConv2d (#37635)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/37635

replaceConvolutionWithConv2d incorrectly assumes that the size of padding is 2. For Conv1d it is 1, in which case we cannot replace with aten::conv2d

Test Plan: Imported from OSS

Differential Revision: D21354930

fbshipit-source-id: a2dbad856666b4bbb2d9015ade8e1704774f20dd",2020-05-04T19:33:20Z,Supriya Rao
bbb6e36495f7133a328cf19ab821b07210956073,"[FSDP2] Fixed `set_requires_gradient_sync`'s `recurse` arg (#124318)

The `recurse` argument was not being respected for `set_requires_gradient_sync`. This PR fixes that.

The previous unit test did not have nested FSDP modules with managed parameters, so the `recurse=False` was not being exercised. We augment the unit test to try only disabling gradient sync for the root module and not children.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/124318
Approved by: https://github.com/weifengpy
ghstack dependencies: #120952, #124293",2024-04-17T20:44:47Z,Andrew Gu
8dee7b7a16ab76c2a82b4032879ab35790e04a68,"Add TORCHDYNAMO_EXTENDED_DEBUG_GUARD_ADDED (#118750)

This allows us to request extended (including C++ backtrace) information
whenever a specific guard occurs.

Signed-off-by: Edward Z. Yang <<EMAIL>>
Pull Request resolved: https://github.com/pytorch/pytorch/pull/118750
Approved by: https://github.com/aakhundov",2024-01-31T18:24:47Z,Edward Z. Yang
62963125f558e27ecfd1838d8acfafcb6a397bf0,"[functorch] Fix parameter declarator cannot be qualified error (pytorch/functorch#361)

Before this change `include_guard` declaration can be interpreted as both local variable declaration and function prototype
Disambiguate it by using curly bracket",2021-12-21T18:20:25Z,Nikita Shulga
9e73e89029d950f3cf9ad4ee7c98ad1fb12ea502,"[quant][core][gpu][improvement] Converted reinterpret_cast<T *>(some_int8_tensor.data_ptr()) calls to some_int8_tensor.data_ptr<int8_t> in quantized cudnn operator files (#75980)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/75980

Support for data_ptr<T> for quantized tensor was enabled in
https://github.com/pytorch/pytorch/pull/75643. Rather than using
reinterpret_cast, we can use this overload directly. The change is
currently made in /aten/src/ATen/native/quantized/cudnn.

(Note: this ignores all push blocking failures!)

Test Plan:
```
python test/test_quantization.py -k test_qlinear_cudnn
python test/test_quantization.py -k test_qconv2d_cudnn
python test/test_quantization.py -k test_qadd_relu_cudnn
```

Reviewed By: jerryzh168

Differential Revision: D35720654

Pulled By: dzdang

fbshipit-source-id: 5ba4b99f6cfaf1b482a0a3f5208c94e53cb05eba
(cherry picked from commit 92e2480fa0862261bff42761d5eab8ee0bb3b075)",2022-04-28T03:22:35Z,dzdang
6dea9927a88aee8ee4db6fd42fb14156c9acc3e4,"Don't use thrust::log(complex) in CUDA as it takes a FOREVER to compile (#107559)

As per title
Pull Request resolved: https://github.com/pytorch/pytorch/pull/107559
Approved by: https://github.com/peterbell10",2023-08-21T00:50:04Z,lezcano
973371139475834c44d118ba0256d97b070980a0,"[JIT] Support calling Tensor.element_size() in TorchScript (#33808)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/33808

# Problem

https://github.com/pytorch/pytorch/issues/33620
ghstack-source-id: 99073701

Test Plan:
```
buck test mode/dev-nosan //caffe2/test:jit -- test_numel

buck test mode/dev-nosan //caffe2/test:jit -- test_element_size

buck build mode/dev-nosan //caffe2/test:jit \
&& buck-out/gen/caffe2/test/jit\#binary.par -r test_numel

buck build mode/dev-nosan //caffe2/test:jit \
&& buck-out/gen/caffe2/test/jit\#binary.par -r test_element_size
```

Compile error

P126667043

Generated code,
```
buck-out/dev/gen/caffe2/generate-code=register_aten_ops_0.cpp/register_aten_ops_0.cpp

buck-out/dev/gen/caffe2/generate-code=register_aten_ops_2.cpp/register_aten_ops_2.cpp
```
P126667064

Differential Revision: D7050644

fbshipit-source-id: 20dbdb9c500b6d7683c23e3049d43ed0ca06d831",2020-02-27T06:22:52Z,Shihao Xu
dc17fb68e4482b1afdf68a4ecf8a6968159d7ad9,Fix minor bug in parallel_apply (#2193),2017-07-24T22:15:00Z,Adam Paszke
61c96811be8051e39b1d5663b2c3538cafb93fd0,"[c10d] NCCL python binding and CI test, with bug fixes (#8357)

* [c10d] NCCL python binding and CI test, with bug fixes

* Addressed comments and further bug fix

* Made NCCL build optional, made C10D libc10d.a only

* Fixed tests so that NCCL pg won't run when not neeeded

* Addressed comments",2018-06-19T20:02:39Z,Teng Li
9cb4bce847af688ca344caaa876d3c47a36f28bf,"Open-source Caffe2 Int8 ops (#13065)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/13065

- Open-source Caffe2 Int8 (quantized) operators

Reviewed By: Yangqing

Differential Revision: D10524381

fbshipit-source-id: 6daa153dc247572900c91e37262d033c368b382d",2018-10-25T19:38:35Z,Marat Dukhan
4b00bce156869ba71475aa3fff80eec553e9da67,"[Gradient Compression] Introduce fp16_compress_wrapper in ddp_comm_hooks.rst (#54052)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/54052

Introduce `fp16_compress_wrapper`, which can give some speedup on top of some gradient compression algorithms like PowerSGD.

ghstack-source-id: 124001805

Test Plan: {F509205173}

Reviewed By: iseessel

Differential Revision: D27076064

fbshipit-source-id: 4845a14854cafe2112c0caefc1e2532efe9d3ed8",2021-03-16T22:38:17Z,Yi Wang
f96bd52841275f8d94a29f3e80c1b4153d55a331,"aot autograd: dont allow symint outputs to get tangents in the bw graph (#96219)

Previously, if dynamic shapes were turned on and we had a forward graph that returns a symint, then we would generate a backward graph that takes in a tangent input for that symint fwd output. This causes problems for downstream - inductor will see an input that it expects to be a symint, but it gets a `None` from autograd.

Confirmed that this repro now passes:
```
benchmarks/dynamo/torchbench.py --devices cuda --inductor --dynamic-shapes --unspecialize-int --accuracy --training --only drq
```

Pull Request resolved: https://github.com/pytorch/pytorch/pull/96219
Approved by: https://github.com/ezyang",2023-03-07T22:58:22Z,Brian Hirsh
13153924cc77b75f009279e26c6c2e92112282e7,"OpInfo porting for msort operator (#55488)

Summary:
Fixes #{issue number}

Pull Request resolved: https://github.com/pytorch/pytorch/pull/55488

Reviewed By: ngimel

Differential Revision: D27708648

Pulled By: iramazanli

fbshipit-source-id: 62b6bc5bd6e54c593b9afac56cb2511411683416",2021-04-12T16:17:40Z,Ilqar Ramazanli
bc9dd969e15afe47b2e0100d5666a77001d71902,"Support inlining no_grad() decorator (#98121)

Pull Request resolved: https://github.com/pytorch/pytorch/pull/98121
Approved by: https://github.com/anijain2305, https://github.com/voznesenskym",2023-04-02T20:36:38Z,Jason Ansel
c757647dd8c198fbcca4d4e6c7cba6f1d12b97e6,"[Better Transformer] make is_causal a hint and force attn_mask to be set on `is_causal=True` in F.MHA (#97214)

Summary:
This fixes an issue raised in [is_causal parameter in torch.nn.TransformerEncoderLayer.forward does not work #96941](https://github.com/pytorch/pytorch/issues/96941) where results computed with is_causal do not properly reflect causal masking.

In PyTorch 2.0, Accelerated PT Transformers added the is_causal parameter to legacy nn.Transformer* and nn.MHA APIs aligned with and intended to engage the is_causal parameter of the new scaled_dot_product_attention (SDPA) operator.

At present is_causal works differently for Transformer* modules, the nn.MHA and F.MHA:
* The nn.Transformer* modules treat is_causal as an optional indicator about the format of attn_mask. This is because some layers (such as the CLIP layer use the attention mask in the layer, and thus the attn_mask was a required feature.)
* Initially, nn.MHA and F.MHA were defined to align with F.SDPA in behavior: a user may specify either the attention mask, or is_causal, but not both.  It seemed to make sense at the time to align SDPA and MHA, esp since there was a larger overlap of parameters which have since changed, e.g., with the removal of need_weights from SDPA. (See below for why this makes sense.)

Unfortunately, this does not work because of how MHA was changed to handle the need_weights parameter.  When need_weights is present, we do not (any more) call SDPA because support for need_weights was removed from SDPA before the release.  The rationale is that need_weights defeats all optimization at the foundation of SDPA performance.  Having the flag might thus mislead users into thinking they get good performance and have them disappointed when they enable a legacy feature of MHA which massively degrades performance.  (They might not think anything of enabling that, because it is on by default in MHA today, which leads to more  issues.)

Since SDPA does not (no longer) support need_weights, we need to pick a separate path which implements attention using a set of discrete operations that allocates a tensor for weights.  Alas, this code path does not have support for is_causal, because attention is implemented as matmul and using the attention mask.  Thus, is_causal has no impact.  (A substantially similar situation arises with how kpm is implemented today because Nested Tensors are not supported by torch.compile() in 2.0)

This problem was masked because all uses of legacy nn.MHA (and F.MHA) come through nn.Transformer* which called self-attention (i.e., nn.MHA) only ever with the attention mask attn_mask, and never with is_causal, a missed optimization opportunit that would have been addressed in a future performance update.

Regrettably, always calling nn.MHA with attn_mask prevented diagnosing of the issue of not having a suitable attention mask when need_weights support was dropped from SDPA and a discrete implementation of attention was added for that scenario, and for the execution path with key_padding_mask.

We have two options to address this issue:

Solution 1: Whenever nn.MHA and F.MHA are executed with is_causal set, we internally create a causal mask at significant expense of allocating a tensor and filling it with a triangular causal matrix.  This increases memory usage, and runtime, for allocating a causal mask.  To add insult to injury, in all current (and likely future) execution scenarios, MHA is called by a model using the nn.Transformer API which already has that matrix and passes it from nn.module to nn.module.  Then the passing in of attn_mask has to be suppressed by nn.TransformerEncoderLayer, only for nn.MHA to immediately allocate the very same tensor again to satisfy the requirement to have an attention mask for the computation. (We expect new use cases to use SDPA directly.)

Solution 2: We align the behavior of nn.MHA and F.MHA with the rest of the existing nn.Transformer API, and require the attention mask to be passed into nn.MHA in addition to is_causal as an optional indicator about the nature of the attention mask rather than as an alternative to attn_mask.  Then, when we choose the code path for processing MHA with need_weights or a key_padding_mask, we have the attn_mask passed down through the nn.Transformer* hierarchy, without the added overhead of allocating an attention mask as in scenario 1.

This PR implements solution 2 which offers better performance and in retrospect aligns MHA better with the rest of the Transformer modules as the definition of SDPA evolved into a more streamlined high-performance operator.  It ostensibly changes how is_causal works, by requiring the attention mask to be specified.  However, as described here, and as shown in the submitted issue, is_causal is not working as intended today, so it requires a change regardless.

In that sense, a change in API does not occur per-se, as the current implementation is not working, and a change has to occur either way to resolve the submitted issue, breaking any use cases that depend on the current implementation.  Checks exist (and more can be added) that flag any scenarios where is_causal is passed as True, but no attention mask is provided, ensuring that there's not quiet change from even the faulty behavior present in 2.0.

As  an upside, the present implementation will improve performance by addressing the passing of the is_causal flag from Transformer modules to MHA, speeding up training for these examples, e.g., finetuning BERT, RoBERTa, XLM-R models.

Differential Revision: *********

Pull Request resolved: https://github.com/pytorch/pytorch/pull/97214
Approved by: https://github.com/albanD",2023-03-25T01:36:26Z,Michael Gschwind
7fa7333299b49a0336164da024b7a51ba9bb7957,"[Distributed][Test] Fix todo in distributed test files (#136836)

Refactor distributed test code:
- Fix TODO: (rohan-varma): remove model
- Fix TODO: add comments for TestTraverse
- Migrate deprecated method call `load_state_dict` and `save_state_dict`

Pull Request resolved: https://github.com/pytorch/pytorch/pull/136836
Approved by: https://github.com/kwen2501",2024-10-16T01:15:12Z,zeshengzong
cbf596bf8ee48f4ed895964fe4faf75a851b49c4,"Sparse CSR CPU: add `addmv_out` (#61536)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/61536

This PR adds CPU dispatch for `addmv_out` with Sparse CSR matrix.
The implementation uses MKL Sparse library. If it's not available then a
runtime error is thrown.
Since structured_delegate is used we only need to implement the out variant, the in-place and normal variants are autogenerated.

MKL descriptor of sparse matrices is implemented in `at::mkl::sparse::MklSparseCsrDescriptor`.
MKL Sparse doesn't allow switching indices type in runtime, it's
predetermined in build time. Only 32-bit version of MKL was tested
locally, but I expect 64-bit version to work correctly as well.

When indices type of PyTorch CSR tensor doesn't match with MKL's,
indices tensor is converted to MKL compatible type (`int` vs `int64_t`).

cc nikitaved pearu cpuhrsch IvanYashchuk

Test Plan: Imported from OSS

Reviewed By: ngimel

Differential Revision: *********

Pulled By: malfet

fbshipit-source-id: b818a0b186aa227982221c3862a594266a58a2a6",2021-11-09T20:30:24Z,Ivan Yashchuk
edfc787df494828bcbb2b05b34ad7a316f647b1e,"Migrate kernels with Tensor? to C10 full dispatcher (#54263)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/54263

Codemod commands generated by https://github.com/pytorch/pytorch/pull/54223

Signatures of the following 8 methods in LegacyTHFunctionsCUDA.h are
manually changed.

```
_thnn_multi_margin_loss_forward
_thnn_multi_margin_loss_backward
_thnn_nll_loss_forward
_thnn_nll_loss_backward
_thnn_nll_loss2d_forward
_thnn_nll_loss2d_backward
_thnn_conv2d_forward
_thnn_conv_depthwise2d_forward
```

ghstack-source-id: 124539990

Test Plan: buck build //caffe2/aten/...

Reviewed By: smessmer

Differential Revision: D27164092

fbshipit-source-id: 59062179ffd958ca253cbf63fdd495799b9a9586",2021-03-22T23:03:47Z,Wenlei Xie
5c809de4b4663c688ebc2cd389c2c866aa22f6e5,Add missing derivatives.yaml input,2017-12-07T21:00:29Z,Zachary DeVito
6e1c18303bcb2a4798d18ea60ee54ab6998c9f80,"unify linear benchmark (#28897)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/28897

as title

Test Plan:
```
buck run mode/opt //caffe2/benchmarks/operator_benchmark/pt:linear_test
# ----------------------------------------
# PyTorch/Caffe2 Operator Micro-benchmarks
# ----------------------------------------
# Tag : short

# Benchmarking PyTorch: linear
# Mode: Eager
# Name: linear_N4_IN256_OUT128_cpu
# Input: N: 4, IN: 256, OUT: 128, device: cpu
Forward Execution Time (us) : 39.275

Reviewed By: hl475

Differential Revision: D18228070

fbshipit-source-id: 9c209eb74e574c6ef85ebcd78b824ef7d5e65dde",2019-10-30T23:23:21Z,Mingzhe Li
e3e6680b489c55e2acd3145503f734f1cd7b7381,"Add ElmanCell and ElmanRNN

Summary: Closes https://github.com/caffe2/caffe2/pull/1742

Reviewed By: dzhulgakov

Differential Revision: D6706809

Pulled By: anderspapitto

fbshipit-source-id: 15a05786a26aeb719ea4377f4dbbb62738d9e697",2018-01-18T19:55:30Z,Anders Papitto
34c7adf1d72177d9fd08d866a018df5c0d5aa2ec,"add Half support for sigmoid on CPU (#96077)

Pull Request resolved: https://github.com/pytorch/pytorch/pull/96077
Approved by: https://github.com/jgong5, https://github.com/ezyang",2023-04-02T09:39:37Z,mingfeima
63dac82444cc522f177b801d9f0cd2e22417c2f4,"Make grad mode error just a warning (#56401)

Summary:
Temporary fix to give people extra time to finish the deprecation.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/56401

Reviewed By: xw285cornell, drdarshan

Differential Revision: D27862196

Pulled By: albanD

fbshipit-source-id: ed460267f314a136941ba550b904dee0321eb0c6",2021-04-20T13:29:27Z,Alban Desmaison
2a1a51facbba6f9be2cc80aa6b91d795666eda46,"Fix typos. (#45195)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/45195

Fix some typos in reducer class.
ghstack-source-id: 112673443

Test Plan: N/A

Reviewed By: rohan-varma

Differential Revision: D23862399

fbshipit-source-id: 0dc69e5ea1fa7d33c85d1909b2216bcd1f579f6a",2020-09-23T21:49:02Z,Yi Wang
4746b3d1fbd8c64da8fca26a156c8321f1f8dcd7,"Added missing VSX dispatch for cholesky_inverse (#51562)

Summary:
It was overlooked that vsx dispatch is also needed for cholesky_inverse cpu dispatch.
See https://github.com/pytorch/pytorch/pull/50269#issuecomment-771688180

Pull Request resolved: https://github.com/pytorch/pytorch/pull/51562

Reviewed By: H-Huang

Differential Revision: D26199581

Pulled By: anjali411

fbshipit-source-id: 5d02c6da52ce1d2e9e26001f5d4648a71dd0e829",2021-02-02T21:33:39Z,Ivan Yashchuk
a4e75ccf85bd580ae5cccd471cfe8aee60dc1aa2,"Registered _like metas (#85793)

Pull Request resolved: https://github.com/pytorch/pytorch/pull/85793
Approved by: https://github.com/ezyang",2022-09-28T08:58:18Z,Horace He
03c660468eb57772e82c1034613f5ff8781c775a,"Removed q_num_blocks from constructor (#130819)

Pull Request resolved: https://github.com/pytorch/pytorch/pull/130819
Approved by: https://github.com/drisspg
ghstack dependencies: #130809, #130818",2024-07-16T20:50:44Z,chilli
978faf1fa29444f78a7ca805f8abc032cb29e0d8,"Use an op counter to decide when to realize a kernel (#117030)

Instead of checking the number of bytes in the string representation
of the kernel

Pull Request resolved: https://github.com/pytorch/pytorch/pull/117030
Approved by: https://github.com/lezcano, https://github.com/peterbell10",2024-01-27T02:28:36Z,Isuru Fernando
65ae897ae84373d1b34df1785880c62fd2b6c80d,"Pin nvidia-container-runtime version (#19195)

Summary:
This PR is to fix the CI error:
```
nvidia-docker2 : Depends: nvidia-container-runtime (= 2.0.0+docker18.09.4-1) but 2.0.0+docker18.09.5-1 is to be installed
E: Unable to correct problems, you have held broken packages.
Exited with code 100
```
Pull Request resolved: https://github.com/pytorch/pytorch/pull/19195

Differential Revision: D14913104

Pulled By: yf225

fbshipit-source-id: d151205f5ffe9cac7320ded3c25baa7e051c3623",2019-04-12T16:57:51Z,Will Feng
915cbf820863135283a3a62b6483bd7c3fa9943e,"[Inductor] Eliminate redundant to_dtype node (#96650)

Pull Request resolved: https://github.com/pytorch/pytorch/pull/96650
Approved by: https://github.com/jgong5, https://github.com/jansel",2023-03-17T06:51:30Z,"Wang, Eikan"
88429a8084d4dce8dc201c5c6ff7597568206261,"[inductor] Add split scan kernel (#117992)

This PR adds a new type of triton kernel in which data is persistent but the
reduction dimension is split over multiple blocks (up to the entire kernel).
though this is called a reduction dimension, in actuality we only support scans.
because of this limitation, i have to be able to block fusions of split scan
operations with reductions so chose to add a new `ir.SplitScan` node which
is identical but allows for differentiation in the scheduler.

The split scan kernel is also the first to require an additional workspace buffer
which is used to communicate between cuda blocks. this is slightly tricky as we
the exact scratch space requirement isn't known until the grid size is calculated.
here i workaround the issue by setting a minimum rblock size and always allocating
to the maximum possible grid size for a given input tensor.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/117992
Approved by: https://github.com/jansel
ghstack dependencies: #117991",2024-02-08T01:00:52Z,Peter Bell
c1aa05f80c46cf960ee0d4c553ea9883ade979fa,"[easy][dynamo] Use disable_dynamo for torch.manual_seed (#126192)

Pull Request resolved: https://github.com/pytorch/pytorch/pull/126192
Approved by: https://github.com/yanboliang
ghstack dependencies: #126191",2024-05-14T18:11:39Z,Animesh Jain
ab18aaeba7c97d210a4c2bb615e962bb43228f17,Clarify output shapes of reduce=False losses (#5082),2018-02-13T18:11:14Z,Richard Zou
27e5299ee3dfe8a48f835e6a8ce11ae697d01937,"[DataPipe] Fix mishandling of exception message when error is not iterable (#84676)

We sometimes get an exception message like this:
```
This exception is thrown by __iter__ of TarArchiveLoaderIterDataPipe(datapipe=FileOpenerIterDataPipe, length=-1, mode='r:')    elif msg not in e.args[0] and single_iterator_msg not in e.args[0]:

TypeError: argument of type 'int' is not iterable
```

The `TypeError` raised by the mishandling of the error message obfuscates the true exception, which now will be show as:
```
FileNotFoundError: [Errno 2] No such file or directory:
```
Pull Request resolved: https://github.com/pytorch/pytorch/pull/84676
Approved by: https://github.com/ejguan",2022-09-08T23:51:21Z,Kevin Tse
d40a7bf9eb25aacf1d4568d8b076c5b6b4fab6d0,Fix Scatter.backward() (#232),2016-11-18T18:58:09Z,Sam Gross
def50d253401540cfdc6c0fffa444d0ee643cc11,"Create a new unstable workflow for periodic jobs (#98858)

And move ROCm distributed job there as it's very flaky in trunk at the moment.  Also move ROCm slow job to `slow` workflow as it should be.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/98858
Approved by: https://github.com/malfet, https://github.com/ZainRizvi",2023-04-11T20:12:23Z,Huy Do
4aeb98dee9756119f6a6414338e92f2b52c83346,"Move RefInfo classes into opinfo.refs (#83563)

Given that there is already a clear `op_db`, `python_ref_db` split I
think it makes sense to have the `RefInfo` classes be defined in a
different file.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/83563
Approved by: https://github.com/albanD",2022-08-19T02:32:17Z,Peter Bell
b652fbc57a331df5aa28b0bcd07f9e72db2fdbae,"Fix torch.nn.functional.gelu docstring formatting (#89061)

The docstring of `torch.nn.functional.gelu` is formatted incorrectly, so that part of the math isn't rendered and there are extra blocks when there shouldn't: https://pytorch.org/docs/stable/generated/torch.nn.functional.gelu.html

I didn't build the docs, so I am not 100% sure that I got the formatting right, but I am confident.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/89061
Approved by: https://github.com/bdhirsh, https://github.com/kit1980",2022-11-18T01:57:38Z,David Boetius
953f39578a7019c4c34bc1dbd6cb0facb554af79,"Mark IPU device as not supports_as_strided (#89130)

Currently causes issues in calls to `.to`.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/89130
Approved by: https://github.com/albanD",2022-11-23T19:51:50Z,Charlie West-Taylor
26f12af53774783337bdd3ac6abe890fc19e28d0,"Fix op benchmarks error in OSS environment (#19518)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/19518

Previous design needs to run the op benchmarks from PyTorch root directory which could lead to `module not found` error in OSS environment. This diff fixes that issue by making the benchmark to be launched in the `benchmarks` folder.

Reviewed By: ilia-cher

Differential Revision: D15020787

fbshipit-source-id: eb09814a33432a66cc857702bc86538cd17bea3b",2019-04-19T23:22:13Z,Mingzhe Li
bc1b4c89125a3ef6d46a60274fe44d755165cbd5,ByteTensor sum test (#6042),2018-03-30T14:58:38Z,cpuhrsch
89d5391bbf38fa0d514c9049a7b05422ce1345d7,"[inductor] Kill mark_node_as_mutating (#130834)

Resubmit of #129346

Pull Request resolved: https://github.com/pytorch/pytorch/pull/130834
Approved by: https://github.com/lezcano
ghstack dependencies: #130832, #130833",2024-07-23T12:10:27Z,Peter Bell
3c2f6d2ecffaee848844a84907d3fe4ccba72629,"[caffe2] Extend dedup SparseAdagrad fusion with stochastic rounding FP16 (#43124)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/43124

Add the stochastic rounding FP16 support for dedup version of SparseAdagrad fusion.
ghstack-source-id: 111037723

Test Plan:
```
buck test mode/dev-nosan //caffe2/caffe2/fb/net_transforms/tests:fuse_sparse_ops_test -- 'test_fuse_sparse_adagrad_with_sparse_lengths_sum_gradient \(caffe2\.caffe2\.fb\.net_transforms\.tests\.fuse_sparse_ops_test\.TestFuseSparseOps\)' --print-passing-details
```

https://our.intern.facebook.com/intern/testinfra/testrun/5629499566042000

```
buck test mode/dev-nosan //caffe2/caffe2/fb/net_transforms/tests:fuse_sparse_ops_test -- 'test_fuse_sparse_adagrad_with_sparse_lengths_mean_gradient \(caffe2\.caffe2\.fb\.net_transforms\.tests\.fuse_sparse_ops_test\.TestFuseSparseOps\)' --print-passing-details
```

https://our.intern.facebook.com/intern/testinfra/testrun/1125900076333177

Reviewed By: xianjiec

Differential Revision: D22893851

fbshipit-source-id: 81c7a7fe4b0d2de0e6b4fc965c5d23210213c46c",2020-09-01T03:33:48Z,Jianyu Huang
e8836759d0898c29262b5370e16970d697cbaf3a,"[export] Add effect token to export (#121424)

Following the creation of effect tokens (https://github.com/pytorch/pytorch/pull/120296), we want to now add support for these tokens in export because the calling/returning convention has changed. The inputs are now `(tokens, params, buffers, constants, user_inputs)` and the outputs are `(tokens, buffer_mutations, user_mutations, user_outputs)`. The graph looks something like:
```
graph():
    %arg0_1 : [num_users=1] = placeholder[target=arg0_1]
    %attr : [num_users=2] = placeholder[target=attr]
    %arg1_1 : [num_users=2] = placeholder[target=arg1_1]
    %with_effects : [num_users=2] = call_function[target=torch._higher_order_ops.effects.with_effects](args = (%arg0_1, _TorchScriptTesting.takes_foo.default, %attr, %arg1_1), kwargs = {})
    %getitem : [num_users=1] = call_function[target=operator.getitem](args = (%with_effects, 0), kwargs = {})
    %getitem_1 : [num_users=1] = call_function[target=operator.getitem](args = (%with_effects, 1), kwargs = {})
    %with_effects_1 : [num_users=2] = call_function[target=torch._higher_order_ops.effects.with_effects](args = (%getitem, _TorchScriptTesting.takes_foo.default, %attr, %getitem_1), kwargs = {})
    %getitem_2 : [num_users=1] = call_function[target=operator.getitem](args = (%with_effects_1, 0), kwargs = {})
    %getitem_3 : [num_users=1] = call_function[target=operator.getitem](args = (%with_effects_1, 1), kwargs = {})
    %add : [num_users=1] = call_function[target=torch.ops.aten.add.Tensor](args = (%arg1_1, %getitem_3), kwargs = {})
    return (getitem_2, add)
```

During unlifting, we will first remove the tokens and with_effect calls using the `remove_effect_tokens` pass. (cc @SherlockNoMad on the pass to remove tokens). This is so that this won't change the calling conventions when retracing. The graph after unlifting looks something like:
```
graph():
    %attr_1 : [num_users=2] = get_attr[target=attr]
    %arg1_1 : [num_users=2] = placeholder[target=arg1_1]
    %takes_foo_default_1 : [num_users=1] = call_function[target=torch.ops._TorchScriptTesting.takes_foo.default](args = (%attr_1, %arg1_1), kwargs = {})
    %takes_foo_default : [num_users=1] = call_function[target=torch.ops._TorchScriptTesting.takes_foo.default](args = (%attr_1, %takes_foo_default_1), kwargs = {})
    %add : [num_users=1] = call_function[target=torch.ops.aten.add.Tensor](args = (%arg1_1, %takes_foo_default), kwargs = {})
    return (add,)
```

Serialization support will be added in a followup.
Note: tokens only affect custom ops that take in ScriptObjects, not ScriptObject methods yet.

Differential Revision: [D54639390](https://our.internmc.facebook.com/intern/diff/D54639390)
Pull Request resolved: https://github.com/pytorch/pytorch/pull/121424
Approved by: https://github.com/tugsbayasgalan",2024-03-08T18:29:12Z,angelayi
ee73c752c67116d36cf268dde011b77df704d507,"Delete unnecessary empty file (#54796)

Summary:
Fixes #{issue number}

Pull Request resolved: https://github.com/pytorch/pytorch/pull/54796

Reviewed By: albanD

Differential Revision: D27370733

Pulled By: iramazanli

fbshipit-source-id: 5f78e9250a545afb91b4bc7b14daa7135a2b6a1b",2021-03-26T20:43:24Z,Ilqar Ramazanli
63429bf4b3f491164f9896701134d81162b27469,"Removed JIT FC tweaks for interpolation options (#71937)

Summary:
Description:
- Removed JIT FC tweaks for interpolation options : nearest-exact and antialiasing

They were added in
- https://github.com/pytorch/pytorch/pull/64501 (Sept 04 2021)
- https://github.com/pytorch/pytorch/pull/65142 (Sept 16 2021)

cc jbschlosser

Pull Request resolved: https://github.com/pytorch/pytorch/pull/71937

Reviewed By: mrshenli

Differential Revision: D33845502

Pulled By: jbschlosser

fbshipit-source-id: 8a94454fd643cd2aef21b06689f72a0f16620d30
(cherry picked from commit b21173d64c27d3ee12b608f2805f209611077aa0)",2022-01-28T19:52:23Z,vfdev
de1b00abdaa0a9fa72c38bcf8b281e60c862bef4,"inductor: tigher upperbound for rblock scaling (#109839)

Previously when we deciding if dynamically scaling down rblock, we use the following formule to compute the upper bound of number of blocks per sm:
```
max_threads_per_multi_processo / (32 * num_warps)
```

This is correct but it's a bit loose and some times because of the loose upper bound, we skip some optimization opportunities.

The new upper bound is: 65536 / n_reg_used_by_each_block . This is a tighter upper bound and can be helpful if the kernel uses too many registers (i.e. much larger than 32).

For kernel https://gist.github.com/shunting314/59aeafd297ed8ff03aa12030a2dd41ae (this is a real kernel inductor generates for HF), the change improve its perf from:
0.485ms    0.332GB    684.29GB/s
to
0.240ms    0.332GB    1382.70GB/s

. The perf is bad previsouly because of register spills

Pull Request resolved: https://github.com/pytorch/pytorch/pull/109839
Approved by: https://github.com/jansel",2023-09-22T00:22:21Z,Shunting Zhang
2fd4d088ff20085cf8af3183d11c9bbdf66a526d,add Adaptive pooling methods to docs,2017-03-27T02:43:46Z,Soumith Chintala
4721553431e968ee5e90753346d92252cce98e84,"[vmap] Fix searchsorted batch rule for self_logical_rank == 0 (#99526)

Fixes #95888

Pull Request resolved: https://github.com/pytorch/pytorch/pull/99526
Approved by: https://github.com/zou3519",2023-04-20T01:49:20Z,Li-Huai (Allan) Lin
fd68b0931f1b1f80a1f389f0722b76ed96f54543,"sym_numel (#82374)

### Description
This PR makes `numel` symint-aware similar to `sym_sizes()` and `sym_strides()`. Similar to https://github.com/pytorch/pytorch/pull/81300 . This PR is the part of a bigger project to support dynamic_shapes.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/82374
Approved by: https://github.com/ezyang",2022-08-03T06:33:45Z,Nikolay Korovaiko
0ca8f66e3aacf3e3ea0fbdd05dd59a59ddcbb8df,"[NestedTensor] Modify softmax on ragged dimension to allow for 2D nested tensors (#132812)

Summary:
Modify `softmax` on the ragged dimension, where `ragged_idx == 1`, to allow for 2D nested tensors. This diff now enables a `softmax` operation on tensors of shape `(B, *)`, where `*` is the ragged dimension.

Extend existing `softmax` unit tests to include 2D nested tensors using the `include_2d_tensor=True` keyword argument.

Test Plan:
Verify that existing and modified unit tests pass using the following commands:

```
buck2 run mode/{opt,inplace} //caffe2/test:nested -- --regex test_softmax
```

```
buck2 run mode/{opt,inplace} //caffe2/test:nested -- --regex test_jagged_op
```

Reviewed By: davidberard98

Differential Revision: D60780975

Pull Request resolved: https://github.com/pytorch/pytorch/pull/132812
Approved by: https://github.com/davidberard98",2024-08-08T15:41:28Z,Janani Sriram
0d03219a421310c2c8c0f287284e27e31ee74562,"Remove hack as integrated builds use FULL_CAFFE2 now (#10320)

Summary:
Signed-off-by: Edward Z. Yang <<EMAIL>>
Pull Request resolved: https://github.com/pytorch/pytorch/pull/10320

Reviewed By: jerryzh168

Differential Revision: D9198902

Pulled By: ezyang

fbshipit-source-id: 8af28d607735e5f4450c40127c1f8c262ea602ce",2018-08-08T04:25:41Z,Edward Yang
8253cfaa72dd00ef74cfa78d7a3c2d1225ab0551,"Conv BN fusion for 3D conv (#10239)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/10239

Make Conv + BN fusion also work for 3D convolutions

Reviewed By: duc0

Differential Revision: D9176314

fbshipit-source-id: 6604aa569c5c3afdb4480a5810890bc617e449c4",2018-08-25T04:23:08Z,Jongsoo Park
bd5ec6c8b7b8d33689109fe258b73ef7a61b9a3c,"[quant][core][gpu][improvement] Removed conv_output and set output tensors as virtual in quantized cudnn conv2d op

Summary:
With support for virtual tensors in cudnn, we no longer have to allocate
conv_output.

Test plan:
```
python test/test_quantization.py -k test_qconv2d_cudnn
```

Pull Request resolved: https://github.com/pytorch/pytorch/pull/76787

Approved by: https://github.com/jerryzh168",2022-05-24T21:27:30Z,dzdang
6b6c63ce5e0b54eb92cdd88f3bb87efc7adeaf9c,"Upstream `argmax` shape function.

Keeping this first commit simple to test out the flow. Will bulk-add the
rest once this one goes through.

Shape function taken from:
https://github.com/llvm/torch-mlir/blob/5192a4e9f3e4cbc77838b70153cd4632aa43dd7f/python/torch_mlir/dialects/torch/importer/jit_ir/build_tools/shape_lib_gen.py#L488

Pull Request resolved: https://github.com/pytorch/pytorch/pull/76592
Approved by: https://github.com/eellison",2022-05-03T16:52:09Z,Sean Silva
064b61009b66c714cb750c2660b7030113ebb131,"Correctly formatting the example in get_state_dict (#119532)

This PR corrects the example formatting provided in https://pytorch.org/docs/stable/distributed.checkpoint.html. In this issue, @wz337 is also commenting that the return type was not showing up correctly. I didn't see any formatting issue, but I could be wrong.

Fixes #118837

Pull Request resolved: https://github.com/pytorch/pytorch/pull/119532
Approved by: https://github.com/fegin",2024-02-12T21:28:22Z,jmarin
4ba3e6758d9d94be111cf3bc735421dfe4dc2b0a,"Canonicalize runtime asserts (#114509)

This allows us to remove quite a few redundant runtime asserts, and potentially a number of guards as well.

On
```
python test/dynamo/test_subclasses.py -k test_unbind
```
we go from
```
inserting runtime assert i0 <= s0
inserting runtime assert 0 <= -i0 + s0
inserting runtime assert i0 + i1 <= s0
inserting runtime assert i0 <= -i1 + s0
inserting runtime assert i0 + i1 + i2 <= s0
inserting runtime assert i0 + i1 <= -i2 + s0
inserting runtime assert Eq(i0 + i1 + i2 + i3, s0)
inserting runtime assert i0 + i1 + i2 + i3 <= s0
inserting runtime assert i0 + i1 + i2 <= -i3 + s0
```
to
```
inserting runtime assert i0 - s0 <= 0
inserting runtime assert i0 + i1 - s0 <= 0
inserting runtime assert i0 + i1 + i2 - s0 <= 0
inserting runtime assert Eq(i0 + i1 + i2 + i3, s0)
```

Pull Request resolved: https://github.com/pytorch/pytorch/pull/114509
Approved by: https://github.com/voznesenskym",2023-11-27T22:24:37Z,lezcano
d833f496021334d21994e600d105f5bf2e0b83d1,"[reland][Inductor] Rename `cpp_wrapper_cuda.py` as `cpp_wrapper_gpu.py` (#136046)

Summary: Reland https://github.com/pytorch/pytorch/pull/135313 after fixing internal build issues

Test Plan: CI

Differential Revision: D62658837

Pull Request resolved: https://github.com/pytorch/pytorch/pull/136046
Approved by: https://github.com/chenyang78, https://github.com/etaf, https://github.com/jansel",2024-09-16T14:35:19Z,Bin Bao
5211fb97ac4c246151f1286c78d63e0e317a8a4a,"Remove device maps from TensorPipe for v1.7 release (#45353)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/45353

Temporarily removing this feature, will add this back after branch cut.

Test Plan: Imported from OSS

Reviewed By: rohan-varma

Differential Revision: D23939865

Pulled By: mrshenli

fbshipit-source-id: 7dceaffea6b9a16512b5ba6036da73e7f8f83a8e",2020-09-25T23:49:32Z,Shen Li
12addc64a6a6787c224e4d96057a797ca7de1535,"Fixed MIOpen RNN Segfault issue and enabled RNN test (#14810)

Summary:
This pull request contains changes for:
1. Added MIOpen RNN API miopenGetRNNLayerBiasSize and miopenGetRNNLayerParamSize.
2. Fixed usage of API miopenGetRNNLayerParam.
3. Modifying the RNN test to run using MIOpen engine.

Differential Revision: D13355699

Pulled By: bddppq

fbshipit-source-id: 6f750657f8049c5446eca893880b397804120b69",2018-12-06T07:52:42Z,lcskrishna
ebb7f20afced4d831ad46e9a88cc63a166cb391a,"quant: make various configs printable (#91419)

Summary:

Makes various quantization configs print out human readable values instead
of just the class name. This is useful when printing these configs out when
debugging.

Test plan:

test script
```
conf_1 = torch.ao.quantization.backend_config.backend_config.DTypeConfig()
print(conf_1)

conf_2 = torch.ao.quantization.backend_config.backend_config.BackendConfig()
print(conf_2)

conf_3 = torch.ao.quantization.backend_config.backend_config.BackendPatternConfig()
print(conf_3)

conf_4 = torch.ao.quantization.fx.custom_config.PrepareCustomConfig()\
    .set_input_quantized_indexes([0])
print(conf_4)

conf_5 = torch.ao.quantization.fx.custom_config.ConvertCustomConfig()\
    .set_preserved_attributes(['foo'])
print(conf_5)

conf_6 = torch.ao.quantization.fx.custom_config.FuseCustomConfig()\
    .set_preserved_attributes(['foo'])
print(conf_6)
```

test script output
```
DTypeConfig(input_dtype_with_constraints=DTypeWithConstraints(dtype=None, quant_min_lower_bound=None, quant_max_
upper_bound=None, scale_min_lower_bound=None, scale_max_upper_bound=None, scale_exact_match=None, zero_point_exa
ct_match=None), output_dtype_with_constraints=DTypeWithConstraints(dtype=None, quant_min_lower_bound=None, quant
_max_upper_bound=None, scale_min_lower_bound=None, scale_max_upper_bound=None, scale_exact_match=None, zero_poin
t_exact_match=None), weight_dtype_with_constraints=DTypeWithConstraints(dtype=None, quant_min_lower_bound=None,
quant_max_upper_bound=None, scale_min_lower_bound=None, scale_max_upper_bound=None, scale_exact_match=None, zero
_point_exact_match=None), bias_dtype=None, is_dynamic=None)
BackendConfig({'name': '', '_pattern_complex_format_to_config': {}})
BackendPatternConfig({'observation_type': <ObservationType.OUTPUT_USE_DIFFERENT_OBSERVER_AS_INPUT: 0>})
PrepareCustomConfig({'input_quantized_indexes': [0]})
ConvertCustomConfig({'preserved_attributes': ['foo']})
FuseCustomConfig({'preserved_attributes': ['foo']})
```
Pull Request resolved: https://github.com/pytorch/pytorch/pull/91419
Approved by: https://github.com/andrewor14",2023-01-04T01:11:37Z,Vasiliy Kuznetsov
f326f7dda8051ec83c81ad725e48600a9d957bf3,"[package] use digraph to back dependency visualization (#57338)

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/57338

Differential Revision: D28114190

Test Plan: Imported from OSS

Reviewed By: astaff

Pulled By: suo

fbshipit-source-id: 78b15edae3b991307fd3656ac7b374d4d218b460",2021-05-06T00:54:20Z,Michael Suo
34804e96009acc8956fefb19c52fcf322d5745e6,"Refactor file and tcp init methods
 * Add sanity checks
 * Refactor InitMethodFile and TCPInitMethod to more logical functions
 * Update few error messages
 * Add passing parameters by **kwargs, so now order of parameters is not relevant
 * Review comments",2017-05-31T09:52:22Z,Janusz Marcinkiewicz
eb6e70cf66129bc6347ff02c3a4116c1b784c628,"[C10D] Only open NCCL dump pipe file once per process (#115798)

The NCCL flight recorder is per-process (it is shared by all
processgroups), but individual process groups used to construct their
own pipe for being signaled to dump the flight recorder.

This ensures that only one pipe per process is created, by only creating
the pipe on the first ProcessGroup (uid_ == 0) which should be the world
group.

Filenames are still keyed off of rank, but this should now be global
rank instead of sub-pg rank, making the filenames unique across the
whole trainer process.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/115798
Approved by: https://github.com/zdevito
ghstack dependencies: #115771",2023-12-14T14:41:03Z,Will Constable
c3e2ee725f3ead3644c7030a051d51d2893b7989,"Automated submodule update: FBGEMM (#42496)

Summary:
This is an automated pull request to update the first-party submodule for [pytorch/FBGEMM](https://github.com/pytorch/FBGEMM).

New submodule commit: https://github.com/pytorch/FBGEMM/commit/87c378172a7fa757a8f0f015f31d4f6111d4744e

Pull Request resolved: https://github.com/pytorch/pytorch/pull/42496

Test Plan: Ensure that CI jobs succeed on GitHub before landing.

Reviewed By: dskhudia

Differential Revision: D22911638

fbshipit-source-id: f20c83908b51ff56d8bf1d8b46961f70d023c81a",2020-08-04T23:09:41Z,Facebook Community Bot
90105a4f3e729844989fec5419b58d916db4a02c,"[ts-migration] Support RaiseException, prim::Unitialized, prim::Enter, and prim::Exit (#129416)

- Support raise exception. It's behavior matches non-strict export now, thanks to @ydwu4's [PR](https://github.com/pytorch/pytorch/pull/128709).
- Support prim::Unitialized, prim::Enter, and prim::Exit
Pull Request resolved: https://github.com/pytorch/pytorch/pull/129416
Approved by: https://github.com/angelayi",2024-07-17T21:59:50Z,Boyuan Feng
a42d093db224528454c58a7cac1b4d9b7126440a,"FCTransposed to FbFCPacked (#29766)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/29766

Add FbgemmPackTranspose op to support the packing on FCTransposed weights

Add FCTransposed to FbFCPacked transformation to Dper fp16 exporter

Test Plan:
```
buck test mode/opt caffe2/caffe2/fb/fbgemm:fb_fc_packed_op_test
```

```
buck test mode/opt caffe2/caffe2/python:layers_test
```

Differential Revision: D18482306

fbshipit-source-id: e8f1947b3d0d04892293509ebf88742f5f0f5997",2019-12-10T18:13:20Z,Summer Deng
8d12ba9acfa20ed7df438a8892c9bf8e6bef5775,"add methods for open device in PackedSequence module. (#124923)

1) add is_{custom_device_name}() and {custom_device_name}() for open device register;
2) fix open device failed testcases.

@ezyang  @bdhirsh
Pull Request resolved: https://github.com/pytorch/pytorch/pull/124923
Approved by: https://github.com/ezyang",2024-04-26T15:26:20Z,Shan19900305
d4f831349756f3739ef69f82a15c86fc677f3eeb,"Add low level torch.profiler.kineto_profile base class (#63302)

Summary:
Refactor torch.profiler.profile by separate it into one low level class and one high level wrapper.

The PR include the following change:
1. separate class torch.profiler.profile into two separated class: kineto_profiler and torch.profiler.profile.
2. The former class has the low-level functionality exposed in C++ level like: prepare_profiler, start_profiler, stop_profiler.
3. The original logics in torch.profiler.profile including export_chrome_trace, export_stacks, key_averages, events, add_metadata are all moved into kineto_profiler since they are all exposed by the torch.autograd.profiler.
4. The new torch.profiler.profile is fully back-compatible with original class since it inherit from torch.profiler.kineto_profiler. Its only responsibility in new implementation is the maintenance of the finite state machine of ProfilerAction.

With the refactoring, the responsibility boundary is clear and the new logic is simple to understand.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/63302

Reviewed By: albanD

Differential Revision: D33006442

Pulled By: robieta

fbshipit-source-id: 30d7c9f5c101638703f1243fb2fcc6ced47fb690",2021-12-14T22:45:36Z,Mike Guo
123297a8c02e0ebbf8b0ae3d3cb16d0dc2e350b4,"[lint] use python to run flake8 and mypy in linter

Previously we were just using whatever version the shell picked up, but
malfet reported that this can (and is) overridden on some machines.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/75858

Approved by: https://github.com/malfet",2022-04-15T04:14:51Z,Michael Suo
de65f156ed6595f0748ff03d27928ddeee3695af,"Add distributed composable API contract (#87580)

Pull Request resolved: https://github.com/pytorch/pytorch/pull/87580
Approved by: https://github.com/yhcharles",2022-10-25T22:30:54Z,Shen Li
54bdaf76d6bda1ca9c1428012deed73ced8dd755,"[PFC] Native UCC process group for Pytorch (#79918)

Summary:
This diff integrates UCC process group as a native component of Pytorch Distributed core. It is based on the existing torch-ucc (https://github.com/facebookresearch/torch_ucc) as the wrapper for UCC collective communication library.
The environment and cmake variables are named in mirroring to the existing process groups such as NCCL and Gloo. Specifically,
- USE_UCC: enables UCC PG. This defaults to OFF, so there is no breakage of existing builds that do not have UCX/UCC external libraries.
- USE_SYSTEM_UCC: uses external UCX and UCC shared libraries that are set accordingly with UCX_HOME and UCC_HOME.

Currently, this diff only supports USE_SYSTEM_UCC=ON, i.e., requiring users to specify external libraries for UCX and UCC. In subsequent diffs, we will add UCX and UCC repos as third-party dependencies in pytorch/third-party.

Test Plan:
Passed Torch-UCC tests that invoke UCC process group. For example:

$ sh test/start_test.sh test/torch_allreduce_test.py --backend gloo --use-cuda
...
Test allreduce: succeeded

Differential Revision: D36973688

Pull Request resolved: https://github.com/pytorch/pytorch/pull/79918
Approved by: https://github.com/kwen2501, https://github.com/kingchc",2022-07-12T14:45:44Z,Terry Lam
3e6164449fe285b7c9c9e4f0df63b5f3ed8a3dc8,"Add efficient zero tensors (#64837)

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/64837

Test Plan: Imported from OSS

Reviewed By: gchanan

Differential Revision: D32834987

Pulled By: anjali411

fbshipit-source-id: 20ea08ade0db0044ca633d9c1a117a6a2e65d1fd",2021-12-08T18:34:08Z,anjali411
8f5fead86ea9a9eac85d20c6aee780e06ce04eb7,"Improves comparison of state dicts for Checkpoint E2E Tests (#113181)

Addresses the following comment - https://github.com/pytorch/pytorch/pull/112541#discussion_r1380197424

Changes the comparison of models in the checkpointing E2E test to compare a non-parallelized model against distribued model after training, saving, & loading.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/113181
Approved by: https://github.com/fegin",2023-11-14T14:54:40Z,Lucas Pasqualin
3897c479af80531a95120811f5ad48db259215ed,"Add API to construct the functional variant of an op (#102293)

`register_functional_op`:
- constructs the functional variant of an op
- registers a functionalization kernel to the op

To get this to work:
- `register_functional_op` makes assumptions that it checks about the
op's schema. In particular, the op is not allowed to return anything it
mutates. We can relax these constraints in the future.
- We add a ""boxed"" python functionalization kernel that handles this
case.

I'm not actually sure (or convinced) this should be public API or how
it should work. If we want this to be public, then it should probably be
a torch.library API, but does that also mean we should give the same
lifetime guarantees? If so, then it would be up to the user to construct
a Library object to actually register the functional variant onto.

Test Plan:
- new tests
Pull Request resolved: https://github.com/pytorch/pytorch/pull/102293
Approved by: https://github.com/bdhirsh",2023-06-01T18:44:57Z,Richard Zou
095886fa423acaa3781334fc1099ef0e22306ab9,"[caffe2] Fix the issues when using CUB RadixSort (#41299)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/41299

When using `cub::DeviceRadixSort::SortPairs` (https://nvlabs.github.io/cub/structcub_1_1_device_radix_sort.html), the `end_bit` argument, or the most-significant bit index (exclusive) needed for key comparison, should be passed with  `int(log2(float(num_rows)) + 1)` instead of `int(log2(float(num_indice)) + 1)`. This is because all the values in indices array are guaranteed to be less than num_rows (hash_size), not num_indices. Thanks ngimel for pointing this point and thanks malfet for quickly fixing the log2() compilation issues.

Note:
An optional bit subrange [begin_bit, end_bit) of differentiating key bits can be specified. This can reduce overall sorting overhead and yield a corresponding performance improvement.

Test Plan:
```
buck test mode/dev-nosan //caffe2/caffe2/fb/net_transforms/tests:fuse_sparse_ops_test -- 'test_fuse_sparse_adagrad_with_sparse_lengths_sum_gradient \(caffe2\.caffe2\.fb\.net_transforms\.tests\.fuse_sparse_ops_test\.TestFuseSparseOps\)' --print-passing-details
```

Reviewed By: malfet

Differential Revision: D22491662

fbshipit-source-id: 4fdabe86244c948af6244f9bd91712844bf1dec1",2020-07-11T05:36:38Z,Jianyu Huang
00459c2c8733d22ecea82047e46bd9087d093ebf,"[primTorch] Implement constant_pad_nd (#80182)

Pull Request resolved: https://github.com/pytorch/pytorch/pull/80182
Approved by: https://github.com/mruberry, https://github.com/ngimel",2022-07-15T11:05:39Z,Peter Bell
772b3e92bfd4ae0ef1f42d11481aab8428de931a,"Parse symbolic shapes (#69775)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/69775

Adds parsing for Symbolic Shapes.

Test Plan: Imported from OSS

Reviewed By: jbschlosser

Differential Revision: D33515233

Pulled By: eellison

fbshipit-source-id: 7ebb22c0ab37d78e459ebcab67bb86f731d00376",2022-01-12T06:09:58Z,Elias Ellison
951582949bd45bbe892d936f58ff4cafa1fd6204,"[export] Enforce final classes in serialization. (#123861)

Summary: as title, these are private API and not meant to be used across repos.

Test Plan: CI

Differential Revision: D56027954

Pull Request resolved: https://github.com/pytorch/pytorch/pull/123861
Approved by: https://github.com/tugsbayasgalan",2024-04-12T15:44:56Z,Zhengxu Chen
345695e8f7a58240fb5ce647d86f44f2d70dea07,"Remove PY37 from binary build matrix (#92919)

Similar to https://github.com/pytorch/test-infra/pull/1416 but for binary build
Pull Request resolved: https://github.com/pytorch/pytorch/pull/92919
Approved by: https://github.com/atalman",2023-01-26T01:25:47Z,Wei Wang
f7dce8508c02f667b20c63fe0f31283f5b4ef279,"Revert *********: [pytorch][PR] Implement cusparse Descriptor class and clean up cusparse code

Test Plan: revert-hammer

Differential Revision:
*********

Original commit changeset: ecbb4063466c

fbshipit-source-id: 56ae47273691a12cc8d96635fb4ad9d09080ccc9",2020-04-29T19:54:42Z,Edward Yang
137f2a385af9a32a71296b8b6e00735c6b1017d7,"[ONNX] Handle sequence output for models (#50599)

Summary:
Duplicate of https://github.com/pytorch/pytorch/issues/46542

Pull Request resolved: https://github.com/pytorch/pytorch/pull/50599

Reviewed By: SplitInfinity

Differential Revision: *********

Pulled By: bzinodev

fbshipit-source-id: a898cef7b2d15a287aedd9798ce1423cebf378d4",2021-01-21T23:29:19Z,neginraoof
493a6ced74ea9271fc7ab16d6493f08750ecb5f4,"[fx] Throw error when symbolically tracing control flow ops (#92313)

Throws a better error when symbolically tracing control flow ops. Right now it throws an error when creating the function arguments.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/92313
Approved by: https://github.com/zhxchen17",2023-01-20T00:38:21Z,Angela Yi
4cc05c41fa26c3b8f76eb4005a5eae8329c1215b,"[MPS] Fix `torch.std` for negative dimentions (#107754)

By simply comparing output dimentions to a properly wrapped dim
Add regression test to opinfo

<!--
copilot:poem
-->
### <samp>🤖 Generated by Copilot at ca98536</samp>

> _`reduceTensor` bug_
> _negative dimensions wrapped_
> _autumn tests added_

Fixes https://github.com/pytorch/pytorch/issues/107116
Pull Request resolved: https://github.com/pytorch/pytorch/pull/107754
Approved by: https://github.com/kit1980",2023-08-23T03:49:59Z,Nikita Shulga
b6e330641aebf1b4acaa887de37ebf07fae0f8c7,"fix Android studio compilation error

Summary: Android studio auto -Werrors in debug mode and throws an error on non string literals in 3rd argument of android_log_print

Reviewed By: Yangqing

Differential Revision: D4465263

fbshipit-source-id: af6dc436b7c98a29aa89bb241c452e6da5c8ad1f",2017-01-26T04:20:32Z,Bram Wasti
eaab653376da76cd3038b7f2bed37b03e2048522,"Read via FileAdapter when loading files in torch if not flatbuffer - Part 2 (#84296)

Summary: D38998858 (https://github.com/pytorch/pytorch/commit/3fae89d4a468a02be501357eb123ce2bf7086d2f) used the wrong version of `_load_for_mobile` that kept the ""load everything in memory then parse"" technique.  This fixes it to call the `_load_for_mobile_impl` version which for non-flatbuffer models will stream parse.  See D38998858 (https://github.com/pytorch/pytorch/commit/3fae89d4a468a02be501357eb123ce2bf7086d2f) for the expected memory optimization gains.

Test Plan: CI Signals.

Reviewed By: qihqi

Differential Revision: D39138280

Pull Request resolved: https://github.com/pytorch/pytorch/pull/84296
Approved by: https://github.com/qihqi",2022-09-01T22:38:59Z,Ian Graves
068c80e6b600b99bb969a8a426eb568c66163fb6,"[BE][MPS] Fix deprecation warnings on MacOS 15.0 (#136292)

[reverseSquareRootWithTensor:](https://developer.apple.com/documentation/metalperformanceshadersgraph/mpsgraph/reversesquareroot(with:name:)?changes=__8&language=objc) were deprecated in favor of [reciprocalSquareRootWithTensor:](https://developer.apple.com/documentation/metalperformanceshadersgraph/mpsgraph/reciprocalsquareroot(_:name:)?changes=__8&language=objc)

Without it, following warnings are generated if compiled on recently released MacOS Sequoia:
```
/Users/<USER>/git/pytorch/pytorch/aten/src/ATen/native/mps/operations/Normalization.mm:720:35: warning: 'reverseSquareRootWithTensor:name:' is deprecated: first deprecated in macOS 15.0 [-Wdeprecated-declarations]
  720 |           rsqrtTensor = [mpsGraph reverseSquareRootWithTensor:varianceEpsTensor name:nil];
      |                                   ^~~~~~~~~~~~~~~~~~~~~~~~~~~
      |                                   reciprocalSquareRootWithTensor
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.0.sdk/usr/include/c++/v1/__type_traits/invoke.h:341:10: note: in instantiation of function template specialization 'at::native::batch_norm_backward_mps(const Tensor &, const Tensor &, const std::optional<Tensor> &, const std::optional<Tensor> &, const std::optional<Tensor> &, const std::optional<Tensor> &, const std::optional<Tensor> &, bool, double, std::array<bool, 3>)::(anonymous class)::operator()<MPSGraph *, CachedGraph *>' requested here
  341 | decltype(std::declval<_Fp>()(std::declval<_Args>()...))
      |          ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.0.sdk/usr/include/c++/v1/__type_traits/invoke.h:351:19: note: while substituting deduced template arguments into function template '__invoke' [with _Fp = (lambda at /Users/<USER>/git/pytorch/pytorch/aten/src/ATen/native/mps/operations/Normalization.mm:623:68) &, _Args = <MPSGraph *, CachedGraph *>]
  351 |   static decltype(std::__invoke(std::declval<_XFp>(), std::declval<_XArgs>()...)) __try_call(int);
      |                   ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.0.sdk/usr/include/c++/v1/__type_traits/invoke.h:357:28: note: while substituting deduced template arguments into function template '__try_call' [with _XFp = (lambda at /Users/<USER>/git/pytorch/pytorch/aten/src/ATen/native/mps/operations/Normalization.mm:623:68) &, _XArgs = (no value)]
  357 |   using _Result = decltype(__try_call<_Fp, _Args...>(0));
      |                            ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.0.sdk/usr/include/c++/v1/__type_traits/conjunction.h:27:32: note: in instantiation of template class 'std::__invokable_r<void, (lambda at /Users/<USER>/git/pytorch/pytorch/aten/src/ATen/native/mps/operations/Normalization.mm:623:68) &, MPSGraph *, CachedGraph *>' requested here
   27 | __expand_to_true<__enable_if_t<_Pred::value>...> __and_helper(int);
      |                                ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.0.sdk/usr/include/c++/v1/__type_traits/conjunction.h:38:39: note: while substituting explicitly-specified template arguments into function template '__and_helper'
   38 | using _And _LIBCPP_NODEBUG = decltype(std::__and_helper<_Pred...>(0));
      |                                       ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.0.sdk/usr/include/c++/v1/__functional/function.h:828:20: note: (skipping 1 context in backtrace; use -ftemplate-backtrace-limit=0 to see all)
  828 |             bool = _And< _IsNotSame<__remove_cvref_t<_Fp>, function>, __invokable<_Fp, _ArgTypes...> >::value>
      |                    ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.0.sdk/usr/include/c++/v1/__functional/function.h:841:49: note: in instantiation of default argument for '__callable<(lambda at /Users/<USER>/git/pytorch/pytorch/aten/src/ATen/native/mps/operations/Normalization.mm:623:68) &>' required here
  841 |   using _EnableIfLValueCallable = __enable_if_t<__callable<_Fp&>::value>;
      |                                                 ^~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.0.sdk/usr/include/c++/v1/__functional/function.h:851:32: note: in instantiation of template type alias '_EnableIfLValueCallable' requested here
  851 |   template <class _Fp, class = _EnableIfLValueCallable<_Fp>>
      |                                ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.0.sdk/usr/include/c++/v1/__functional/function.h:852:25: note: in instantiation of default argument for 'function<(lambda at /Users/<USER>/git/pytorch/pytorch/aten/src/ATen/native/mps/operations/Normalization.mm:623:68)>' required here
  852 |   _LIBCPP_HIDE_FROM_ABI function(_Fp);
      |                         ^~~~~~~~~~~~~
/Users/<USER>/git/pytorch/pytorch/aten/src/ATen/native/mps/operations/Normalization.mm:623:68: note: while substituting deduced template arguments into function template 'function' [with _Fp = (lambda at /Users/<USER>/git/pytorch/pytorch/aten/src/ATen/native/mps/operations/Normalization.mm:623:68), $1 = (no value)]
  623 |     auto cachedGraph = LookUpOrCreateCachedGraph<CachedGraph>(key, [&](auto mpsGraph, auto newCachedGraph) {
      |                                                                    ^
/Users/<USER>/git/pytorch/pytorch/aten/src/ATen/native/mps/operations/Normalization.mm:623:24: note: while substituting deduced template arguments into function template 'LookUpOrCreateCachedGraph' [with T = CachedGraph]
  623 |     auto cachedGraph = LookUpOrCreateCachedGraph<CachedGraph>(key, [&](auto mpsGraph, auto newCachedGraph) {
      |                        ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.0.sdk/System/Library/Frameworks/MetalPerformanceShadersGraph.framework/Headers/MPSGraphArithmeticOps.h:123:1: note: 'reverseSquareRootWithTensor:name:' has been explicitly marked deprecated here
  123 | -(MPSGraphTensor *) reverseSquareRootWithTensor:(MPSGraphTensor *) tensor
      | ^
/Users/<USER>/git/pytorch/pytorch/aten/src/ATen/native/mps/operations/Normalization.mm:745:37: warning: 'reverseSquareRootWithTensor:name:' is deprecated: first deprecated in macOS 15.0 [-Wdeprecated-declarations]
  745 |             rsqrtTensor = [mpsGraph reverseSquareRootWithTensor:varianceEpsTensor name:nil];
      |                                     ^~~~~~~~~~~~~~~~~~~~~~~~~~~
      |                                     reciprocalSquareRootWithTensor
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.0.sdk/System/Library/Frameworks/MetalPerformanceShadersGraph.framework/Headers/MPSGraphArithmeticOps.h:123:1: note: 'reverseSquareRootWithTensor:name:' has been explicitly marked deprecated here
  123 | -(MPSGraphTensor *) reverseSquareRootWithTensor:(MPSGraphTensor *) tensor
      | ^
2 warnings generated.
```
Pull Request resolved: https://github.com/pytorch/pytorch/pull/136292
Approved by: https://github.com/kit1980",2024-09-18T23:38:31Z,Nikita Shulga
d996acfbc2e7536b0438eb491628a328bdf59f05,"[XNNPACK] disable ARM_BF16 and ARM_FP16_VECTOR (#94020)

Summary: This is not used and will cause build failure

Test Plan: CI

Differential Revision: D42982023

Pull Request resolved: https://github.com/pytorch/pytorch/pull/94020
Approved by: https://github.com/Skylion007, https://github.com/tiandiao123, https://github.com/digantdesai",2023-02-03T05:00:58Z,Hansong Zhang
b34bb1f562421dbcc497bd625ffbd1814bbb9291,"Add support for parsing torch.Generator in JIT (#140489)

Fixes #140420

Pull Request resolved: https://github.com/pytorch/pytorch/pull/140489
Approved by: https://github.com/davidberard98",2024-11-13T23:06:54Z,Antonio Kim
ea50549ce62aeeccfe27035a0a975e83b9c2c987,"Suppress guards when creating fake tensors (#89349)

When we create fake tensors, we may call operators that introduce
guards, to accurately reconstruct views.  But these guards are spurious:
if a user is able to present a tensor that ""looks the same"", they have
implicitly fulfilled the contract that the view is creatable.

Signed-off-by: Edward Z. Yang <<EMAIL>>
Pull Request resolved: https://github.com/pytorch/pytorch/pull/89349
Approved by: https://github.com/voznesenskym",2022-11-21T23:12:21Z,Edward Z. Yang
d6a8d397dab2f8e31639c694fd7e9591c9a72fa7,"Fix formatting for merge failed message (#95234)

Fixes formatting so that the merge rule shows up on a different line than the ""Raised by"" text

Follow up to https://github.com/pytorch/pytorch/pull/94932

New version
<img width=""433"" alt=""image"" src=""https://user-images.githubusercontent.com/4468967/220441349-ac99096d-590a-42c1-b995-4a23b2d9b810.png"">
Pull Request resolved: https://github.com/pytorch/pytorch/pull/95234
Approved by: https://github.com/huydhn",2023-02-22T18:11:22Z,Zain Rizvi
2f6e8e84c51df80b4e783e5840c9443fbbce7a3c,"Fix `_chunk_cat.out` issue (#122076)

# PR
Vectors allocated inside `get_chunk_cat_metadata()` are out of local scope when used in `_chunk_cat_out_cuda_contiguous()`. This PR fixes the issue by returning vectors from `get_chunk_cat_metadata`.
This PR also added a few unit tests to cover more edge cases.

# Tests
This PR is tested with the following command and no error shows. So the flaky test error should be resolved.

- `PYTORCH_NO_CUDA_MEMORY_CACHING=1 compute-sanitizer python test/test_ops.py -v -k test_out__chunk_cat_cuda_float32`
- `PYTORCH_NO_CUDA_MEMORY_CACHING=1 python test/test_ops.py -v -k test_out__chunk_cat_cuda_float32 --repeat 1500`

Fixes #122026
Fixes #121950

Pull Request resolved: https://github.com/pytorch/pytorch/pull/122076
Approved by: https://github.com/yifuwang",2024-03-20T20:01:34Z,Boyuan Feng
94e197c262dad3fb3839e4de7ce2a4b2a9c30ecb,"Add utf-8 header to Python file with Unicode. (#8131)

Signed-off-by: Edward Z. Yang <<EMAIL>>",2018-06-04T21:49:32Z,Edward Z. Yang
a7933acd5a2a9ac0154a5e272a9c8a36dc4eb1d1,"Improve custom ops aliasing error message (#134688)

Fixes https://github.com/pytorch/pytorch/issues/134278

Test Plan:
- tested locally
Pull Request resolved: https://github.com/pytorch/pytorch/pull/134688
Approved by: https://github.com/yushangdi
ghstack dependencies: #134466, #134490, #134491, #134690, #134692",2024-08-28T17:17:01Z,rzou
d5b38984c844044bf28b0283f7be5186087cd097,"Let RPC return FutureIValue instead of FutureMessage (#37519)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/37519

closes #37446

Currently FutureMessage is used in several places:

1. `rpc_async` returns a `FutureMessage` object and we expose it
   as `torch.distributed.rpc.Future`. From applications perspective,
   they are expecting a `py::object` instead of a `Message`, and we
   do the conversion in the `Future.wait()` pybind method.
2. RPC autograd profiler takes `FutureMessage` and installs
   callbacks to it. The profiler actually only need a `Future<T>`
   and does not care what `T` is.
3. `OwnerRRef` exposes a `getFuture()` API which returns a
   `FutureMessage`. This `FutureMessage` will be marked completed
   when the value referenced by the `OwnerRRef` is ready.
   `OwnerRRef` does not need it to be a Message type either, it
   actually creates an empty `Message` to mark the `Future`.

The above places are using `FutureMessage`, but they don't really
need a `Message`, and `Message` is a communication layer type that
applications or profiler or the RRef shouldn't be aware of.

Another motivation for making this change is that for async RPC
UDF #36071, we are going to allow application to call
`markCompleted` in Python. If we still use `FutureMessage`, then
in the `markCompleted` pybind function, it needs to convert the
provided `py::object` into a specific message type, which is
leaking communication layer code to pybind functions. Even if
this is doable, we will have two entities (RPC agent and pybind
Python frontend) accessing the same request callback logic. This is too messy.

This commit replaces all surface `FutureMessage` with `FutureIValue`,
so that `FutureMessage` is no longer visible from Python land. Note
that this does not cause BC issues, as the Python Future type name
and its API stay intact. Internally, we still have `FutureMessage`
in the communication layer.

Test Plan: Imported from OSS

Reviewed By: xush6528

Differential Revision: D21308887

Pulled By: mrshenli

fbshipit-source-id: 4f574f38e83125081f142813cfdde56119522089",2020-04-30T02:05:29Z,Shen Li
2ed3a73e404bed0795bc18c51af3d92de80dbd97,"[dynamo] treat `torch.device`, `torch.dtype` as constant literal; revise guards to have access to `torch` module (#112426)

Just like e.g. container - list/set of constant literals, these are constant literals.

We follow up to https://github.com/pytorch/pytorch/pull/112416, enforcing that we always use `ConstantVariable` to represent these.

Replace https://github.com/pytorch/pytorch/pull/112284, https://github.com/pytorch/pytorch/pull/112332 as incomplete, in case there is no movement there.

Ought to fix: https://github.com/pytorch/pytorch/issues/109910

We remove old guards special-casing, which fell back on str equality when not having access to `torch` module in `eval`

Pull Request resolved: https://github.com/pytorch/pytorch/pull/112426
Approved by: https://github.com/ezyang",2023-11-01T05:28:24Z,Jon Chuang
7362e22f8b780488490bb819d6f7d6e6f7469bcb,"Notify on outdated lintrunner (#96241)

Let users know if they have an outdated version of lintrunner installed on their box

Sets the minimum version to one which uses master as a default mergebase (see https://github.com/pytorch/pytorch/pull/95938)
Pull Request resolved: https://github.com/pytorch/pytorch/pull/96241
Approved by: https://github.com/huydhn",2023-03-08T18:41:31Z,Zain Rizvi
9d37cefcb0c1e867efcfe15c6c0d734ab3b14c84,"Resubmit _int_mm (#96685)

Avoids any changes to gemm_and_bias

Pull Request resolved: https://github.com/pytorch/pytorch/pull/96685
Approved by: https://github.com/drisspg, https://github.com/ngimel",2023-03-27T16:14:07Z,Christian Puhrsch
4883d39c6fd38431bdc60e1db6402e251429b1e1,"Avoid direct reference to at::native::tensor from TensorDataContainer (#47567)

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/47567

Test Plan: Imported from OSS

Reviewed By: ezyang

Differential Revision: D24822517

Pulled By: iseeyuan

fbshipit-source-id: f69bfc029aae5199dbc63193fc7a5e5e6feb5790",2020-11-18T01:30:37Z,Martin Yuan
a4f0f8b1e95636c302931cf8964aa80b495ecfa3,"[distributed] add base processgroup::options (#53662)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/53662

Add a base processgroup::options so that we can do inheritance and
provide
a universal option API in python

Test Plan: Imported from OSS

Reviewed By: rohan-varma

Differential Revision: D26968856

Pulled By: wanchaol

fbshipit-source-id: 858f4b61b27aecb1943959bba68f8c14114f67d8",2021-03-18T01:38:15Z,Wanchao Liang
f595467e5c6569d4a457033d7ee46c7b9b1d28b1,"Reenable slow gradcheck and make it pass (#80514)

Context: For a while slow gradcheck CI was skipping nearly all tests and this hid the fact that it should've been failing and timing out (10+h runtime for TestGradients). The CI configuration has since been fixed to correct this, revealing the test failures. This PR reenables slow gradcheck CI and makes it pass again.

This PR:
- makes slow and failing tests run in fast gradcheck mode only
- reduce the input size for slow gradcheck only for unary/binary ufuncs (alternatively, skip the test entirely)
- skip entire test files on slow gradcheck runner if they don't use gradcheck (test_ops, test_meta, test_decomp, test_ops_jit)
- reduces the input size for some ops

Follow ups:
1. Investigate slow mode failures https://github.com/pytorch/pytorch/issues/80411
2. See if we can re-enable slow gradcheck tests for some of the slow tests by reducing the sizes of their inputs

The following are failing in slow mode, they are now running in fast mode only.
```
test_fn_fwgrad_bwgrad___rmod___cuda_float64
test_fn_fwgrad_bwgrad_linalg_householder_product_cuda_complex128
test_fn_fwgrad_bwgrad__masked_prod_cuda_complex128
test_fn_fwgrad_bwgrad__masked_prod_cuda_float64
test_fn_fwgrad_bwgrad_linalg_matrix_power_cuda_complex128
test_fn_fwgrad_bwgrad_cat_cuda_complex128
test_fn_fwgrad_bwgrad_linalg_lu_factor_ex_cuda_float64
test_fn_fwgrad_bwgrad_copysign_cuda_float64
test_fn_fwgrad_bwgrad_cholesky_inverse_cuda_complex128
test_fn_fwgrad_bwgrad_float_power_cuda_complex128
test_fn_fwgrad_bwgrad_fmod_cuda_float64
test_fn_fwgrad_bwgrad_float_power_cuda_float64
test_fn_fwgrad_bwgrad_linalg_lu_cuda_float64
test_fn_fwgrad_bwgrad_remainder_cuda_float64
test_fn_fwgrad_bwgrad_repeat_cuda_complex128
test_fn_fwgrad_bwgrad_prod_cuda_complex128
test_fn_fwgrad_bwgrad_slice_scatter_cuda_float64
test_fn_fwgrad_bwgrad_tile_cuda_complex128
test_fn_fwgrad_bwgrad_pow_cuda_float64
test_fn_fwgrad_bwgrad_pow_cuda_complex128
test_fn_fwgrad_bwgrad_fft_*
test_fn_fwgrad_bwgrad_zero__cuda_complex128
test_fn_gradgrad_linalg_lu_factor_cuda_float64
test_fn_grad_div_trunc_rounding_cuda_float64
test_fn_grad_div_floor_rounding_cuda_float64
```

Marks the OpInfos for the following ops that run slowly in slow gradcheck as `fast_gradcheck` only (the left column represents runtime in seconds):
```
0  918.722  test_fn_fwgrad_bwgrad_nn_functional_conv_transpose3d_cuda_float64
1  795.042  test_fn_fwgrad_bwgrad_nn_functional_unfold_cuda_complex128
2  583.63  test_fn_fwgrad_bwgrad_nn_functional_max_pool3d_cuda_float64
3  516.946  test_fn_fwgrad_bwgrad_svd_cuda_complex128
4  503.179  test_fn_fwgrad_bwgrad_linalg_svd_cuda_complex128
5  460.985  test_fn_fwgrad_bwgrad_linalg_lu_cuda_complex128
6  401.04  test_fn_fwgrad_bwgrad_linalg_lstsq_grad_oriented_cuda_complex128
7  353.671  test_fn_fwgrad_bwgrad_nn_functional_max_pool2d_cuda_float64
8  321.903  test_fn_fwgrad_bwgrad_nn_functional_gaussian_nll_loss_cuda_float64
9  307.951  test_fn_fwgrad_bwgrad_stft_cuda_complex128
10  266.104  test_fn_fwgrad_bwgrad_svd_lowrank_cuda_float64
11  221.032  test_fn_fwgrad_bwgrad_istft_cuda_complex128
12  183.741  test_fn_fwgrad_bwgrad_lu_unpack_cuda_complex128
13  132.019  test_fn_fwgrad_bwgrad_nn_functional_unfold_cuda_float64
14  125.343  test_fn_fwgrad_bwgrad_nn_functional_pad_constant_cuda_complex128
15  124.2  test_fn_fwgrad_bwgrad_kron_cuda_complex128
16  123.721  test_fn_fwgrad_bwgrad_pca_lowrank_cuda_float64
17  121.074  test_fn_fwgrad_bwgrad_nn_functional_max_unpool3d_cuda_float64
18  119.387  test_fn_fwgrad_bwgrad_rot90_cuda_complex128
19  112.889  test_fn_fwgrad_bwgrad__masked_normalize_cuda_complex128
20  107.541  test_fn_fwgrad_bwgrad_dist_cuda_complex128
21  106.727  test_fn_fwgrad_bwgrad_diff_cuda_complex128
22  104.588  test_fn_fwgrad_bwgrad__masked_cumprod_cuda_complex128
23  100.135  test_fn_fwgrad_bwgrad_nn_functional_feature_alpha_dropout_with_train_cuda_float64
24  88.359  test_fn_fwgrad_bwgrad_mH_cuda_complex128
25  86.214  test_fn_fwgrad_bwgrad_nn_functional_max_unpool2d_cuda_float64
26  83.037  test_fn_fwgrad_bwgrad_nn_functional_bilinear_cuda_float64
27  79.987  test_fn_fwgrad_bwgrad__masked_cumsum_cuda_complex128
28  77.822  test_fn_fwgrad_bwgrad_diag_embed_cuda_complex128
29  76.256  test_fn_fwgrad_bwgrad_mT_cuda_complex128
30  74.039  test_fn_fwgrad_bwgrad_linalg_lu_solve_cuda_complex128
```
```
0  334.142  test_fn_fwgrad_bwgrad_unfold_cuda_complex128
1  312.791  test_fn_fwgrad_bwgrad_linalg_lu_factor_cuda_complex128
2  121.963  test_fn_fwgrad_bwgrad_nn_functional_max_unpool3d_cuda_float64
3  108.085  test_fn_fwgrad_bwgrad_diff_cuda_complex128
4  89.418  test_fn_fwgrad_bwgrad_nn_functional_max_unpool2d_cuda_float64
5  72.231  test_fn_fwgrad_bwgrad___rdiv___cuda_complex128
6  69.433  test_fn_fwgrad_bwgrad___getitem___cuda_complex128
7  68.582  test_fn_fwgrad_bwgrad_ldexp_cuda_complex128
8  68.572  test_fn_fwgrad_bwgrad_linalg_pinv_cuda_complex128
9  67.585  test_fn_fwgrad_bwgrad_nn_functional_glu_cuda_float64
10  66.567  test_fn_fwgrad_bwgrad_lu_cuda_float64
```
```
0  630.13  test_fn_gradgrad_nn_functional_conv2d_cuda_complex128
1  81.086  test_fn_gradgrad_linalg_solve_triangular_cuda_complex128
2  71.332  test_fn_gradgrad_norm_cuda_complex128
3  64.308  test_fn_gradgrad__masked_std_cuda_complex128
4  59.519  test_fn_gradgrad_div_no_rounding_mode_cuda_complex128
5  58.836  test_fn_gradgrad_nn_functional_adaptive_avg_pool3
```

Reduces the sizes of the inputs for:
- diff
- diag_embed

Pull Request resolved: https://github.com/pytorch/pytorch/pull/80514
Approved by: https://github.com/albanD",2022-07-21T20:42:35Z,soulitzer
40ada91161ca73952f4fda4cfec7c4710070b061,"[PyTorch][easy] Fix borrowing from optional in binary_cross_entry_with_logits

Pull Request resolved: https://github.com/pytorch/pytorch/pull/78266

Saves a refcount bump.

Differential Revision: [D36650626](https://our.internmc.facebook.com/intern/diff/D36650626/)

Approved by: https://github.com/jbschlosser",2022-05-31T16:47:06Z,Scott Wolchok
2268cae9c7952b71a6b42feae327dcebd0fba6f1,atan2 implementation,2014-11-07T19:19:58Z,soumith
e2dc1fc715d3d6add9eba3fe0a23cf277c7df66d,"Add a bitwise NOT operator for integer and Boolean types (CPU).

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/22283

Test Plan: Imported from OSS

Differential Revision: D16183576

Pulled By: colesbury

fbshipit-source-id: 2e539fab8ff885dddb9bff334d1d784b28d65b8f",2019-07-10T19:03:07Z,Hong Xu
dbb31a2984fa616b4bb6fac7abb2a06ec0533eb1,"[Inductor] Add triton.autotune support for user defined triton kernels with constant/simple grids (#112228)

Pull Request resolved: https://github.com/pytorch/pytorch/pull/112228
Approved by: https://github.com/jansel",2023-10-27T07:48:33Z,Oguz Ulgen
14b409952135256de2d37f2fa2cf7ce8a3f6d84a,"[FSDP2] support torch._foreach_copy_(float8) for fully_shard(Float8Linear) (#135955)

this PR unblocks unit test with single Float8Linear module. It fixes following error
```
torch._foreach_copy_(foreach_copy_dsts, all_gather_inputs)
[rank0]:E0913 13:44:29.829000 2179476 torch/testing/_internal/common_distributed.py:671] RuntimeError: ""foreach_tensor_copy"" not implemented for 'Float8_e4m3fn'
```

Differential Revision: [D63961071](https://our.internmc.facebook.com/intern/diff/D63961071)
Pull Request resolved: https://github.com/pytorch/pytorch/pull/135955
Approved by: https://github.com/vkuzo, https://github.com/eqy",2024-10-07T03:03:23Z,Wei Feng
070169e4d0cb6ddab61b83cffe4cf93807a39087,"[ATen] tensor.contiguous() -> tensor.expect_contiguous (#55022)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/55022

Replace tensor.contiguous() with tensor.expect_contiguous in aten::narrow_copy

Test Plan: CI

Reviewed By: edvgha

Differential Revision: D27453866

fbshipit-source-id: c5a6e64ccca4cf52cb879dfb02fd4c451fb397cb",2021-04-01T18:20:42Z,Hao Lu
54056c1705bdfd6e8b077b1cd501c6f36d0e192a,"Update cudnn_frontend to 0.7.3 (#93272)

Updating cudnn_frontend to 0.7.3 To enable CUDNN 8.7 integration

Pull Request resolved: https://github.com/pytorch/pytorch/pull/93272
Approved by: https://github.com/malfet, https://github.com/Skylion007",2023-01-30T20:45:00Z,atalman
6e1e27fc4e36edc7d8dad602de7e8250ad16073b,"[inductor] Refactor pre-grad passes into inductor.fx_passes (#99130)

Pull Request resolved: https://github.com/pytorch/pytorch/pull/99130
Approved by: https://github.com/ngimel",2023-04-15T03:16:34Z,Jason Ansel
31808dcdd8ec2c10699e6df7fd305f724c9ece8b,"[RELAND] [CUDA graphs] Make CUDAGeneratorImpl capturable (ci-all edition) (#48694)

Summary:
Resubmission of https://github.com/pytorch/pytorch/pull/47989 with attempted fix for the unexpected context creation that caused revert (https://github.com/pytorch/pytorch/pull/47989#issuecomment-736689145).

Submitting from a ci-all branch because the failing test isn't public.

Diffs relative to master should be the same as https://github.com/pytorch/pytorch/pull/47989 's approved diffs, aside from the fix itself https://github.com/pytorch/pytorch/pull/48688/commits/a5c80f63d3aae66d691bbafc726615e9be8e68be.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/48694

Reviewed By: mruberry

Differential Revision: D25291431

Pulled By: ngimel

fbshipit-source-id: 8c27f85c64eecaf1f5cb925020fa6d38a07ff095",2020-12-04T20:33:13Z,Michael Carilli
7557a993ab9b38583d5fdbebd25d927f31ffb7a9,"Allow dataloader to accept a custom memory pinning function (#14171)

Summary:
Currently, the `pin_memory_batch` function in the dataloader will return a batch comprised of any unrecognized type without pinning the data, because it doesn't know how.

This behavior was preventing us from overlapping data prefetching in Mask-RCNN, whose custom `collate_fn` returns a custom batch type.

The present PR adds the ability for the user to pass a `pin_fn` alongside any custom `collate_fn` to handle such custom types.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/14171

Differential Revision: *********

Pulled By: soumith

fbshipit-source-id: ca965f9841d4a259b3ca4413c8bd0d8743d433ab",2018-11-23T16:08:35Z,Michael Carilli
fa799132d82c3c48253aaf7d3ee3a8c5e007350d,"[MPS] Better error message for `slow_conv2d_forward` (#86303)

Error `Could not run 'aten::_slow_conv2d_forward' with arguments from the 'MPS' backend.` is very misleading as usually this method is only invoked if input is on CPU but weights are on MPS device.
Raise a more user friendly error in this case

Add test to `test_invalid_conv2d` to check for those conditions.

Fixes https://github.com/pytorch/pytorch/issues/77931

Pull Request resolved: https://github.com/pytorch/pytorch/pull/86303
Approved by: https://github.com/kulinseth",2022-10-06T15:38:57Z,Nikita Shulga
1dbf44c00d4f77e72fcdf48ecf7628c3bcac4f96,Add SmoothL1Loss to functional,2017-01-15T19:11:32Z,Adam Paszke
1ece1ab6c2c5488b8475c70681aebddbdb9579ba,"[ci] print rerun stacktraces for pytest (#86831)

example: https://github.com/pytorch/pytorch/actions/runs/3238428826/jobs/5306808276

Pull Request resolved: https://github.com/pytorch/pytorch/pull/86831
Approved by: https://github.com/huydhn",2022-10-14T17:31:31Z,Catherine Lee
1d53d0756668ce641e4f109200d9c65b003d05fa,"Add docs to CI (#24435)

Summary:
Stacked PRs
 * #24445 - [jit] Misc doc updates #2
 * **#24435 - [jit] Add docs to CI**

This integrates the [doctest](http://www.sphinx-doc.org/en/master/usage/extensions/doctest.html) module into `jit.rst` so that we can run our code examples as unit tests. They're added to `test_jit.py` under the `TestDocs` class (which takes about 30s to run). This should help prevent things like #24429 from happening in the future. They can be run manually by doing `cd docs && make doctest`.

* The test setup requires a hack since `doctest` defines everything in the `builtins` module which upsets `inspect`
* There are several places where the code wasn't testable (i.e. it threw an exception on purpose). This may be resolvable, but I'd prefer to leave that for a follow up. For now there are `TODO` comments littered around.
](https://our.intern.facebook.com/intern/diff/16840882/)
Pull Request resolved: https://github.com/pytorch/pytorch/pull/24435

Pulled By: driazati

Differential Revision: D16840882

fbshipit-source-id: c4b26e7c374cd224a5a4a2d523163d7b997280ed",2019-08-21T04:39:09Z,davidriazati
9d80969fa47a4f921f43adc60762cbec895bdaf8,"Retry brew and gem installation in trunk ios workflow (#96970)

Per title, I don't want to see network flakiness like this https://github.com/pytorch/pytorch/actions/runs/4439991996/jobs/7793213476 ever again :P
Pull Request resolved: https://github.com/pytorch/pytorch/pull/96970
Approved by: https://github.com/clee2000",2023-03-16T21:30:57Z,Huy Do
65e87052e7f8db05fa7c692cb9ea46a8424f4b54,TH: fix speed issue due to calls to THTensor_nElement(),2014-03-06T18:02:51Z,Ronan Collobert
b0833533a779d656cd6e9f6d103956ff105e7ef5,"Update internal code for torch.linalg.solve (#56613)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/56613

Replace linalg_solve_helper with `lu_stub` + `lu_solve_stub`.
Once `lu_stub` and `lu_solve_stub` have cuSOLVER-based codepath,
`torch.linalg.solve` will have it as well.

Test Plan: Imported from OSS

Reviewed By: agolynski

Differential Revision: D28379394

Pulled By: mruberry

fbshipit-source-id: b47f66bc1ee12715da11dcffc92e31e67fa8c8f6",2021-05-13T23:55:56Z,Ivan Yashchuk
dee43798d762d5282f373598a8f97aaf0f19ae07,"Revert ""Create a new Ubunutu 22.04 (jammy) build for platform010 (#77591)""

This reverts commit 71d82917f45920d096da421f33ce0057cace611f.

Reverted https://github.com/pytorch/pytorch/pull/77591 on behalf of https://github.com/zengk95 due to this is breaking linux slow test on trunk",2022-06-18T00:10:06Z,PyTorch MergeBot
3a4c0900c737fe73f900f0d21fc21d972f9bbd2e,"Reland 3 of Merge more symbolic meta kernels and symint changes from branch (#86795)

Take 3
Contains:
- symintification of split*
- floor support on SymFloat
- pad_backward, gather, scatter meta
Pull Request resolved: https://github.com/pytorch/pytorch/pull/86795
Approved by: https://github.com/z-a-f",2022-10-17T02:09:40Z,albanD
8f627fc658542db4a9442a3e57791371463f03d2,"CharTensor should be signed (#5512)

CharTensor is actually int8_t which is signed",2018-03-02T16:34:10Z,Sam Gross
bb157dd4eb21019ff396e5ce123d115914dd04a5,"Make methods of internal file_obj visible from StreamWrapper (#71653)

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/71653

Test Plan: Imported from OSS

Reviewed By: NivekT

Differential Revision: D33718749

Pulled By: ejguan

fbshipit-source-id: f3a8244f22ca37049b8678afa0e329b23c957a9d
(cherry picked from commit a4d12ca48ec153ad5f058152e7df4a9a1421b184)",2022-01-25T14:57:41Z,Erjia Guan
6894bb0a853982e2ef39d0d0bf8160bfc17dd3c2,"Remove on_green and mandatory_only (#96400)

Our default behavior is on green, and currently the two main modes are on green and force.
Surprisingly, both these flags are pretty much not used anywhere.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/96400
Approved by: https://github.com/huydhn",2023-03-09T17:38:49Z,Catherine Lee
****************************************,"[ROCm] Update triton pin to fix libtanh issue (#125396)

Fixes #ISSUE_NUMBER

Pull Request resolved: https://github.com/pytorch/pytorch/pull/125396
Approved by: https://github.com/pruthvistony, https://github.com/nmacchioni",2024-05-30T19:26:58Z,Prachi Gupta
e392d428b11128bcf203a4fe5b5f24b04bf545c9,"Allowing TaskGroups to carry remote nets (#14342)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/14342

Sometimes, when we are creating a TaskGroup, we are in fact creating a TaskGroup for a distributed job. In some cases, we may want to register a few nets as ""remote"" to a TaskGroup. The remote net should have sufficient attributes on where they should be executed later on.

This diff adds the remote net attribute to the TaskGroup class. It exposes two minimal functionalities: adding a remote net, and getting all remote nets added to a TaskGroup.

Reviewed By: d4l3k

Differential Revision: D13188320

fbshipit-source-id: efe947aec30817e9512a5e18be985713b9356bdc",2018-11-27T21:31:59Z,Hassan Eslami
8269f7b6527fa16b9fc914e80a0c3a8f0964b58b,"Delete redundant THC_API on THCStorage_new (#30312)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/30312

It's not necessary because it's already defined in the header.

Signed-off-by: Edward Z. Yang <<EMAIL>>

Test Plan: Imported from OSS

Differential Revision: D18762363

Pulled By: ezyang

fbshipit-source-id: 418bf355d460dd171ac449559f20bf55415e54ae",2019-12-03T18:41:25Z,Edward Yang
0bd2955f158d8041657c447d82944d159e07b801,"Memory leak from bsr_scatter_mm_indices_data argument cache (#112301)

Pull Request resolved: https://github.com/pytorch/pytorch/pull/112301
Approved by: https://github.com/cpuhrsch, https://github.com/pearu",2023-11-01T21:04:32Z,Andrew M. James
d68df54269552dc8312b7c2eba23be20c255c59f,"OpInfo: fill_ (#59138)

Summary:
Reference: https://github.com/pytorch/pytorch/issues/54261

Pull Request resolved: https://github.com/pytorch/pytorch/pull/59138

Reviewed By: ngimel

Differential Revision: D28776451

Pulled By: mruberry

fbshipit-source-id: 2e8e9f1805ec7d900223ea749a4a0b86a1bedb54",2021-05-29T07:32:55Z,kshitij12345
5ecb966e0ff383d65531c8f6de23e704b9cafc54,"Add ciflow-tracking issue to pytorch-probot (#64125)

Summary:
Doesn't do anything yet...

Pull Request resolved: https://github.com/pytorch/pytorch/pull/64125

Reviewed By: zhouzhuojie

Differential Revision: D30620283

Pulled By: malfet

fbshipit-source-id: 91869d35c1b70a55e32261d2c32fb0136ec33960",2021-09-01T00:33:11Z,Nikita Shulga
b45b9673a16ba02fdeaee4ada9ed30c9b757a44a,"Fixes clang format (#36787)

Summary:
Fixes clang format.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/36787

Differential Revision: D21084603

Pulled By: mruberry

fbshipit-source-id: 7e29da135f9a2aa126cb68640e33c1914fd570e3",2020-04-17T07:40:23Z,Mike Ruberry
c7db642a72d80facf9c8a17f42ac0bb04d04723f,"Adding collective quantization API (#62142)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/62142

Created wrapper that takes the collective op and a quantization type as an arguments. It quantize the input, performs the collective op, and and perform dequantization

Test Plan:
Tested through distributed_gloo_fork.
e.g., buck test mode/dev-nosan caffe2/test/distributed:distributed_nccl_fork -- test_all_to_all_quantized

Reviewed By: wanchaol

Differential Revision: D29682812

fbshipit-source-id: 79c39105ff11270008caa9f566361452fe82a92e",2021-08-09T15:09:49Z,Marjan Fariborz
13538c88b38b8fe038e344ab0a588bb2462193c9,"[1/n] Consolidate `replicate` and `DDP`: setup ufmt for `distributed.py` (#96597)

As we already enabled ufmt for composable APIs in https://github.com/pytorch/pytorch/pull/90873, it seems a good idea to enable ufmt for other distributed APIs as well. This change setup ufmt for DDP.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/96597
Approved by: https://github.com/rohan-varma",2023-03-17T03:12:23Z,Charlie Yan
a2cb9b7331524eb0d9e62b38c57d38d8725cbc1b,"Flip triton kernel default layout constraint to ""needs_fixed_stride_order"" (#135581)

This is to match the default layout constraint for custom operators. By
default, Inductor should match the stride order of inputs to a triton
kernel.

Test Plan:
- existing tests

Pull Request resolved: https://github.com/pytorch/pytorch/pull/135581
Approved by: https://github.com/eellison
ghstack dependencies: #135530",2024-09-10T15:00:03Z,rzou
2ac34b98ea0e5c2b975b9d144e8589a9c9e829d3,"[auto] Update onnx to 490c4c6 - fix build dependency between onnx-operators.proto and (onnx/onnx#934)
https://github.com/onnx/onnx/commit/490c4c6ca99bceed0499bab3535b43917dea0537",2018-05-13T03:14:44Z,onnxbot
f1f3bd8c36d20dde9ef6f69f294cc365a2c5eb2c,"Back out ""Revert D31005792: [NCCL] Init dummy NCCL comms in constructor"" (#65883)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/65883

Original commit changeset: d8e962b8aab6
ghstack-source-id: 139836954

Test Plan: ci

Reviewed By: zhaojuanmao

Differential Revision: D31299350

fbshipit-source-id: 9ad5c8fa17f7038ba579cb1eda6d9271ac07a130",2021-10-08T22:58:27Z,Rohan Varma
a732bbea232fa32191f259d7cb15e9fabb6c2926,"[meta] Add meta support for fft ops (#79311)

As per title
Pull Request resolved: https://github.com/pytorch/pytorch/pull/79311
Approved by: https://github.com/ezyang",2022-06-13T01:56:42Z,kshitij12345
cbb76eae0479faa629c5eb1164916f183209fe07,add int and long tests for abs and fix recursive bug in abs,2016-09-10T20:40:27Z,soumith
2dd23ebfdb4a4581ebeebe9b51002652ead11369,"Add support for multi output nodes in partial eval graph stitching (#66097)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/66097

Adding logic to generate runtime shapes for nodes with multi-outputs. It is generalizing existing flow of looking at a node, getting its shape graph, inlining it, and adding a mapping from the output to the new value in the stitched shape compute graph to loop over multiple outputs.

Test Plan: Imported from OSS

Reviewed By: navahgar

Differential Revision: D31797468

Pulled By: eellison

fbshipit-source-id: 2c182b71a46b36d33f23ad35b89790a4a5d4471c",2021-10-20T23:09:33Z,Elias Ellison
8b4c4875818ab2193e27fb004aa052f755c4ca0c,"Fix AOTInductor complication on ROCM (#134522)

Summary:
Original PR (https://github.com/pytorch/pytorch/pull/124123) is broken by cpp_builder refactoring

So resubmit it to fix

Test Plan: Test with command here: https://www.internalfb.com/phabricator/paste/view/P1549765548

Differential Revision: D61827208

Pull Request resolved: https://github.com/pytorch/pytorch/pull/134522
Approved by: https://github.com/frank-wei",2024-08-29T21:59:04Z,Zhuoran Zhao
d3f98b5ffc991d22df9b2783ec43a5148b824e6d,"Add matrix power (#11421)

Summary:
vishwakftw Your patch needed some updates because the default native function dispatches changed from `[function, method]` to `[function]`. The CI was run before that change happened so it still shows green, but the internal test caught it.

I did some changes when rebasing and updating so I didn't just force push to your branch. Let's see if this passes CI and internal test. If it does, let me know if you want me to force push to your branch or use this PR instead.

Note to reviewers: patch was already approved at #10068 .

cc yf225
Pull Request resolved: https://github.com/pytorch/pytorch/pull/11421

Differential Revision: D9733407

Pulled By: SsnL

fbshipit-source-id: cf2ed293bb9942dcc5158934ff4def2f63252599",2018-09-08T22:21:14Z,Tongzhou Wang
3e1859959a3f720bb5f5e47c3ca15fb3cbfae4da,"Updating submodules

Summary:
GitHub commits:

https://github.com/facebook/fbthrift/commit/2b59db7359f555b87f1565d2024057369ed8bdc2
https://github.com/facebook/folly/commit/242186f5ff988bd9a243401833614acdace99f25
https://github.com/facebook/mcrouter/commit/4bf6682fe6e1a8497489333b83c69b0fbab78aac
https://github.com/facebook/rocksdb/commit/fe238e54389934ed3d1b96e226d5bed62eb29188
https://github.com/facebook/watchman/commit/e8cf50093e18b2bc40a2c4e5fd6f32f181e05e0a
https://github.com/pytorch/fbgemm/commit/17d9a609f24c2829de7be6ad10977dee35915347

Test Plan: n/a

Reviewed By: yns88

fbshipit-source-id: 3c1030ebc3768a827583b50c2d47fba494816943",2020-04-30T18:51:17Z,svcscm
ee777a7c3c3c263bb6dca15a61e8755bb0dab231,"docs: Add docstring for torch.masked._ops.logaddexp (#113206)

logaddexp is not a reduction and normalization, so
_apply_docstring_templates cannot be used to add a docstring.

Fixes https://github.com/pytorch/pytorch/issues/113082

Also fix another misspelling.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/113206
Approved by: https://github.com/cpuhrsch",2023-11-08T22:45:32Z,BJ Hargrave
cc49f5abd33857d3e143c03303150c0c14e09142,"[Re-land 90265] [inductor] add conv_transpose2d unary fusion for cpu in inference mode (#91954)

Re-land https://github.com/pytorch/pytorch/pull/90265.
Depend on internal ideep upgrade.
[Update]: internal ideep upgrade issue is resolved in https://github.com/pytorch/pytorch/pull/92239.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/91954
Approved by: https://github.com/jgong5, https://github.com/desertfire",2023-01-31T06:18:34Z,chunyuan
291cbf09263c786b0e6277ebef8927d00bc130b5,"[functorch] Introduce ForceLocalDispatchKeySet

IncludeDispatchKeyGuard and ExcludeDispatchKeyGuard were getting too
confusing",2021-12-13T14:58:09Z,Richard Zou
a6fa6a6cda06038b9b5a800c4c4eae70974d0684,"[fx minimizer] Add an option to minimizer to allow return all intermediate results (#57279)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/57279

Added an option ""return_intermediate"". If true, when building the submodule we want to run , we will replace the output with all the nodes, so that intermediate results of all the nodes will be returned as output.

This is recommended to use with `run_node()` function.

Test Plan: `buck test glow/fb/nnpi/lowering:net_min_tests`

Reviewed By: khabinov

Differential Revision: D27913887

fbshipit-source-id: 5a3eab02da05214fb9adeb25656c267b58075b1d",2021-04-29T20:45:10Z,Shiyan Deng
874f9bd5090c1f57afdfbd454952ed4f71d210cc,"[FX] Gate FXGraphDrawer on whether pydot is installed (#65088)

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/65088

Test Plan: Imported from OSS

Reviewed By: khabinov

Differential Revision: D30967951

Pulled By: jamesr66a

fbshipit-source-id: dba2f13a47889b3d4187de925b4fe74ee90b7f79",2021-09-16T17:00:59Z,James Reed
130881f0e37cdedc0e90f6c9ed84957aee6c80ef,"Delete build_caffe2.sh, replace with build_libtorch.py (#10508)

Summary:
delete build_caffe2.sh, replace with build_libtorch.py as suggested by peter (and copy-pasted from his draft PR).  This ensures that all consumers of the torch CMake file go through as unified a path as possible.

In order to change the surrounding infrastructure as little as possible, I made some tweaks to enable build_pytorch_libs.sh to generate the test binaries relative to the current directory, rather than hardcoding to pytorch/build.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/10508

Differential Revision: D9354398

Pulled By: anderspapitto

fbshipit-source-id: 05b03df087935f88fca7ccefc676af477ad2d1e9",2018-08-16T14:57:00Z,Anders Papitto
e85f3fccb33d041621e81616f7404a9d991e8681,"Fix relying on UB in test_data_parallel_nested_output (#11092)

Summary:
We shouldn't reply on plain `dict` ordering. Example failure: https://ci.pytorch.org/jenkins/job/pytorch-builds/job/pytorch-linux-xenial-cuda8-cudnn6-py3-test1/8417/console
Pull Request resolved: https://github.com/pytorch/pytorch/pull/11092

Reviewed By: ezyang

Differential Revision: D9583274

Pulled By: SsnL

fbshipit-source-id: ba80b96648c98c24c2ec5fa6fd9aa566c095cce7",2018-08-30T19:56:16Z,Tongzhou Wang
adb2b380baf1d78a5e4a48d8a6999b94aaeff403,"[quant][graphmode][fx] qconfig_dict support more types of configurations (#44856)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/44856

Support following format of qconfig_dict
```python
qconfig_dict = {
    # optional, global config
    """": qconfig?,

    # optional, used for module and function types
    # could also be split into module_types and function_types if we prefer
    ""object_type"": [
      (nn.Conv2d, qconfig?),
      (F.add, qconfig?),
      ...,
    ],

    # optional, used for module names
    ""module_name"": [
      (""foo.bar"", qconfig?)
      ...,
    ],

    # optional, matched in order, first match takes precedence
    ""module_name_regex"": [
      (""foo.*bar.*conv[0-9]+"", qconfig?)
      ...,
    ]
    # priority (in increasing order): global, object_type, module_name_regex, module_name
    # qconfig == None means fusion and quantization should be skipped for anything
    # matching the rule
}
```

Test Plan: Imported from OSS

Reviewed By: vkuzo

Differential Revision: D23751304

fbshipit-source-id: 5b98f4f823502b12ae2150c93019c7b229c49c50",2020-09-23T20:53:52Z,Jerry Zhang
8e78a1b084d3856da6406ab8627c1343cc48e16f,"[Resubmit] Fix for incorrect usage of logging in torch/distributed/distributed_c10d.py (#52757)

Summary:
Resubmit of https://github.com/pytorch/pytorch/pull/51739
Fixes https://github.com/pytorch/pytorch/issues/51428

Pull Request resolved: https://github.com/pytorch/pytorch/pull/52757

Reviewed By: cbalioglu

Differential Revision: D26646843

fbshipit-source-id: df4962ef86ea465307e39878860b9fbbcc958d52",2021-04-06T18:31:17Z,Szymon Migacz
eea680a3549c3de1cad73d9eb75ddd417fb1e711,"[auto] Update onnx to 31ca96c - Microbenchmark for encoding+decoding ModelProto and GraphProto with a single operator (#609)
https://github.com/onnx/onnx/commit/31ca96ca3331d05884a71c38975d34870eb9c81d",2018-03-15T03:21:09Z,onnxbot
b62827b81a1a1ba618b57e924c09947f482c6990,"[Quant][devs] Separated implementations for quantized & non-quantized tensors in fill_ (#71939)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/71939

This PR is part of a series of PRs addressing https://github.com/pytorch/pytorch/issues/54150,
related to using dispatcher for calls to quantized backends as opposed to if/else conditionals.
This particular PR separates the calls to quantized & non-quantized backends for fill_
using a dispatcher.

Differential Revision:
D33827371
D33827371

Test Plan: Imported from OSS

Reviewed By: jerryzh168

Pulled By: dzdang

fbshipit-source-id: d034f83de844ef777a2d71e5464f582cba634550
(cherry picked from commit 9f38385051e41a32ccc631dc3354caa03188649b)",2022-02-03T15:38:39Z,dzdang
ce409d8f500b2f916af3976f8e0030ae5f257363,"docs: clarify smooth l1 == l1 when beta == 0 (#70673)

Summary:
Fixes https://github.com/pytorch/pytorch/issues/68558.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/70673

Reviewed By: albanD

Differential Revision: D33430267

Pulled By: jbschlosser

fbshipit-source-id: db92187ff4f2799b19a6c4a5a6b653e9211c3aca",2022-01-05T22:34:18Z,Jake Tae
6d222116a13d55c2aa2211938f9df686535fbd51,"[Documentation] Minor  rendering issue (#84856)

There is a Rendering issue with the docstring of nn.GELU.

Hope this fixes the [issue.](https://pytorch.org/docs/stable/generated/torch.nn.GELU.html)

cc: @malfet
Pull Request resolved: https://github.com/pytorch/pytorch/pull/84856
Approved by: https://github.com/kit1980",2022-09-13T00:29:50Z,Abhijit Deo
008a8c9720183d7bf8b00bf64d8d21c62270089f,Implement lgamma function.,2017-04-20T23:24:14Z,ethanluoyc
dfd822d756067f114b73d4d455c1d8c467e108ad,"Fix deserialization for UpsamplingBilinear2d (#101248)

Fixes #100935 , adding handling for the recompute_scale_factor field. I would be happy to write a test for this, but might need some advice on where it should go/how to reliably reproduce the given issue. I'd also be happy to iterate on the proposed changes.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/101248
Approved by: https://github.com/albanD",2023-05-12T15:40:13Z,ts
aa7e27fa7049c9e6621c093ead7f13258ca53e14,"Emit Loop Condition as Separate Block (#21611)

Summary:
Emit loop condition as a separate block in loops, then inline them before conversion to SSA. This is needed for breaks & continues where we will inline the condition block after the continue pass and before the break pass.

I also considered emitting a prim::For and a prim::While, but i think it's easier to just have one pathway.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/21611

Differential Revision: *********

Pulled By: eellison

fbshipit-source-id: de17c5e65f6e4a0256a660948b1eb630e41b04fb",2019-06-12T05:00:02Z,Elias Ellison
314a502eb04c6382e2cc9af0573533efba54109d,"Revert ""Reland ""[C10] PG observability hooks. (#108815)"" (#110907)""

This reverts commit 7678cd22af46c9df4fb47a409d3e8ad71a6127ea.

Reverted https://github.com/pytorch/pytorch/pull/110907 on behalf of https://github.com/huydhn due to Sorry for reverting this, but macos job in trunk starts failing after this https://hud.pytorch.org/pytorch/pytorch/commit/7678cd22af46c9df4fb47a409d3e8ad71a6127ea ([comment](https://github.com/pytorch/pytorch/pull/110907#issuecomment-1756497387))",2023-10-11T00:23:42Z,PyTorch MergeBot
18cf30fb2ab4c80491a56127789bf513885dc9e5,"[Inductor] preserve AliasedLayout on View (#96948)

Fix https://github.com/pytorch/pytorch/issues/96728

Pull Request resolved: https://github.com/pytorch/pytorch/pull/96948
Approved by: https://github.com/Chillee",2023-03-17T01:16:19Z,Jiong Gong
cba79f48726a5e74e8a0e96b3a70cc8ccff60880,"Revert *********: [wip] Replace Type dispatch with ATenDispatch

Differential Revision:
*********

Original commit changeset: fcfaea0b5480

fbshipit-source-id: 9bca7ebb91d7a3609b86663089140d7c5a33f58d",2019-06-20T00:28:17Z,Ailing Zhang
f54eac7eba9ce60fe03032fd8cd96cfcf2ef6e6a,Add flag and warning for Python 2.7 users on Windows (#6499),2018-04-12T03:06:51Z,peterjc123
22a77d7b92b62a114830986299e4f593ca868c5f,"[warning] Disable broken assert (#71778)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/71778

This assert was broken (never triggers).  Fixing the assert leads to test failures.  We need to fix those test failures, so a FIXME has been filed.  The urgency is avoiding the compile time failure that will come with enabling `-Wstring-conversion` as an error.

Test Plan: CI Pass

Reviewed By: r-barnes

Differential Revision: D33754171

fbshipit-source-id: 834b070b94007af583d0fc6c022f23b6703f3fbc
(cherry picked from commit ac8f905fb11c75b470b964f5ff5157e79d4c4b60)",2022-01-25T19:28:24Z,Nolan O'Brien
768b7c0dee34b614ab1cd8f89c69ec7d86c19c88,Static linking against libstdc++ in Binary Build mode,2017-07-14T20:27:20Z,Soumith Chintala
23fad9111e816c5ee0e6c327ff9f067399b0b3a6,"[quant][graphmode][fx] Add additional_qat_module_mapping (#46344)

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/46344

Test Plan: Imported from OSS

Reviewed By: vkuzo

Differential Revision: D24317438

fbshipit-source-id: f9e73aeb4c7a107c8df0bae8319464e7d5d7275b",2020-10-22T20:02:55Z,Jerry Zhang
1bf3dc51ae68bccb672145daeb4715bf08b78556,"[JIT] Add `__prepare_scriptable__` duck typing to allow replacing nn.modules with scriptable preparations (#45645)

Summary:
Fixes https://github.com/pytorch/pytorch/issues/45072

As discussed with zdevito gchanan cpuhrsch and suo, this change allows developers to create custom preparations for their modules before scripting. This is done by adding a `__prepare_scriptable__` method to a module which returns the prepared scriptable module out-of-place. It does not expand the API surface for end users.

Prior art by jamesr66a: https://github.com/pytorch/pytorch/pull/42244

cc: zhangguanheng66

Pull Request resolved: https://github.com/pytorch/pytorch/pull/45645

Reviewed By: dongreenberg, ngimel

Differential Revision: D24039990

Pulled By: zhangguanheng66

fbshipit-source-id: 4ddff2d353124af9c2ef22db037df7e3d26efe65",2020-11-10T16:58:02Z,Donny Greenberg
018c3420b80c7bbdb87caa91f0430a90988872a4,"Make dim, numel, element_size into prim ops (#36551)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/36551

Before, those ops were special cased in the jit codegen but that blocks our unboxing refactoring.
Instead, make those regular prim ops.
ghstack-source-id: 102081858

Test Plan: waitforsandcastle

Differential Revision: D21009196

fbshipit-source-id: b90320fce589fc0553f17582b66a5a05d0fd32d1",2020-04-14T09:15:49Z,Sebastian Messmer
977a66fe8807f4b64cba021825723a8366355ee2,"switch DimensionNode's base from TsNode to Node

Switching the base of DimensionNode to TsNode so it can be reused by XLA and other backends.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/75916
Approved by: https://github.com/alanwaketan",2022-04-16T02:57:29Z,Nikolay Korovaiko
081b56fd41de591669bd1f61d4aadd7eb65ec335,"Improve readability of cuda_lazy_init (#80788)

This PR cleans up the implementation of `cuda_lazy_init.cpp` and improves its readability. No behavioral changes are introduced.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/80788
Approved by: https://github.com/ezyang",2022-07-04T16:47:09Z,Can Balioglu
02dd1f38f289ec1cadef086f7bc0fcc15666a928,"[pytorch] CUDA kernel for torch.cat on contiguous tensors with wide loads (#102815)

This PR creates a CUDA kernel for `CatArrayBatchedCopy` that makes use of vectorized memory loads to maximize HBM bandwidth. It also simplifies the kernel code by removing the path handling not-contiguous inputs.  It gets called when the following conditions are met:

- tensors are contiguous
- input data types are of 32bit and 64 bit
- all the input are aligned to 16 bytes boundary

We tested on a larger set of problem sizes and there is net gain for 32 bit types and marginal gain for 64 bit types. Based on our analysis the 32 bit cats are by far the dominant kernel being called.

Results:

<img width=""1320"" alt=""Screenshot 2023-06-02 at 8 10 21 AM"" src=""https://github.com/pytorch/pytorch/assets/23515689/6f083f7c-2e1a-4513-a994-e0cb072d9b5d"">

The SASS Code confirms using the wide loads for input tensors and the stores to global memory are unrolled to maximize oversubscription:

<img width=""1648"" alt=""Screenshot 2023-06-02 at 8 16 29 AM"" src=""https://github.com/pytorch/pytorch/assets/23515689/10325ee6-d3a0-402a-af0d-29cd1a32813b"">

Test Code:

```python
import sys

import torch

l_inputs = [
    ((1024,), 0, 2, 100),
    ((4096,), 0, 2, 100),
    ((16384,), 0, 4, 100),
    ((32000,), 0, 8, 100),
    ((128 * 1024,), 0, 2, 100),
    ((256 * 1024,), 0, 3, 100),
    ((1 * 1024 * 1024,), 0, 2, 100),
    ((4 * 1024 * 1024,), 0, 2, 100),
    ((16 * 1024 * 1024,), 0, 2, 100),
    ((32 * 1024 * 1024,), 0, 2, 100),
    ((128 * 1024 * 1024,), 0, 2, 50),
    ((64, 256), 0, 4, 100),
    ((400, 400), 0, 2, 100),
    ((640, 1080), 0, 2, 100),
    ((128, 4096), 1, 2, 100),
    ((512, 512), 1, 2, 100),
    ((699, 713), 1, 2, 100),
    ((1024, 1024), 1, 2, 100),
    ((2000, 1000), 1, 2, 100),
    ((4096, 4096), 1, 2, 100),
    ((16384, 16384), 1, 2, 50),
    ((384, 256, 16), 1, 2, 100),
    ((400, 200, 13), 1, 2, 100),
    ((128, 64, 256), 0, 2, 100),
    ((512, 256, 256), 1, 2, 100),
    ((512, 1024, 1024), 2, 2, 10),
    ((1024, 512, 1024), 2, 2, 10),
    ((1024, 1024, 512), 2, 2, 10),
    ((128, 64, 64, 32), 0, 2, 50),
    ((128, 64, 128, 16), 1, 2, 50),
    ((100, 45, 45, 32), 3, 2, 50),
    ((128, 32, 256, 32), 3, 2, 50),
]

prof_inputs = [
    ((1234567,), 0, 2, 5),
    ((16 * 1024 * 1024,), 0, 3, 5),
    ((1013, 1013), 0, 2, 5),
    ((1024, 1024), 1, 2, 5),
    ((69, 74, 128), 0, 2, 5),
    ((128, 128, 128), 2, 2, 5),
]

def generate_tensors(dim_tuple, cat_type, num_tensors):
    if cat_type in [torch.int8, torch.int32, torch.int64]:
        l_tensors = [
            torch.randint(
                high=torch.iinfo(cat_type).max,
                size=dim_tuple,
                dtype=cat_type,
                device=""cuda"",
            )
        ] * num_tensors
        return l_tensors
    else:
        l_tensors = [
            torch.randn(dim_tuple, dtype=cat_type, device=""cuda"")
        ] * num_tensors
        return l_tensors

def test_simple_cat(
    dim_tuple, cat_dim: int, num_tensors: int, iterations: int, cat_type
):
    torch.cuda.synchronize()

    # Allocate a tensor equal to L2 cache size on A100 GPUs
    l2_cache_flusher = torch.empty(
        int(80 * (1024**2)), dtype=torch.float, device=""cuda""
    )

    # All the tensors in the list get read and written once
    total_MB = 2 * num_tensors
    for dim in dim_tuple:
        total_MB *= dim
    total_MB /= 1024 * 1024

    # Get the number of bits per element
    if cat_type in [torch.int8, torch.int32, torch.int64]:
        total_MB *= torch.iinfo(cat_type).bits / 8
    else:
        total_MB *= torch.finfo(cat_type).bits / 8

    l_tensors = generate_tensors(dim_tuple, cat_type, num_tensors)
    c = torch.cat(l_tensors, dim=cat_dim)
    torch.cuda.synchronize()

    # Measure correctness
    l_tensors_cpu = []
    for t in l_tensors:
        l_tensors_cpu.append(t.detach().to(""cpu""))
    c_cpu = torch.cat(l_tensors_cpu, dim=cat_dim)
    c_cpu_dev = c.detach().to(""cpu"")

    if not torch.equal(c_cpu, c_cpu_dev):
        missmatches = torch.count_nonzero(torch.abs(c_cpu - c_cpu_dev))
        print(""Error; num missmatches for {0} = {1}"".format(dim_tuple, missmatches))
        return

    # Measure a few iterations
    l_ev_start = [torch.cuda.Event(enable_timing=True)] * iterations
    l_ev_stop = [torch.cuda.Event(enable_timing=True)] * iterations

    l_cat_times = []
    torch.cuda.synchronize()
    for i in range(iterations):
        l2_cache_flusher.zero_()
        torch.cuda._sleep(1_000_000)

        l_ev_start[i].record()
        c = torch.cat(l_tensors, dim=cat_dim)
        l_ev_stop[i].record()
    torch.cuda.synchronize()

    for i in range(iterations):
        t_cat = l_ev_start[i].elapsed_time(l_ev_stop[i]) / 1000
        l_cat_times.append(t_cat)

    min_cat_time = min(l_cat_times)

    # return bandwidth in GB/s
    estimated_bw_GBps = total_MB / min_cat_time / 1024
    return estimated_bw_GBps

def main(argv):
    if len(argv) > 0:
        if ""profile"" in str(argv[0]):
            for l_input in prof_inputs:
                gbps = test_simple_cat(
                    l_input[0], l_input[1], l_input[2], l_input[3], torch.float
                )
                print(
                    ""Bandwidth (GB/s) for {0} fp32 | {1:.2f}"".format(
                        (l_input[0], l_input[1]), gbps
                    )
                )
            return

    for l_input in l_inputs:
        gbps_int8 = test_simple_cat(
            l_input[0], l_input[1], l_input[2], l_input[3], torch.int8
        )
        gbps_fp16 = test_simple_cat(
            l_input[0], l_input[1], l_input[2], l_input[3], torch.float16
        )
        gbps_fp32 = test_simple_cat(
            l_input[0], l_input[1], l_input[2], l_input[3], torch.float32
        )
        gbps_int32 = test_simple_cat(
            l_input[0], l_input[1], l_input[2], l_input[3], torch.int32
        )
        gbps_fp64 = test_simple_cat(
            l_input[0], l_input[1], l_input[2], l_input[3], torch.float64
        )
        gbps_long = test_simple_cat(
            l_input[0], l_input[1], l_input[2], l_input[3], torch.long
        )

        print(
            ""Bandwidth (GB/s) for {0} int8;fp16;fp32;int32;fp64;long|{1:.2f}|{2:.2f}|{3:.2f}|{4:.2f}|{5:.2f}|{6:.2f}"".format(
                (l_input[0], l_input[1]),
                gbps_int8,
                gbps_fp16,
                gbps_fp32,
                gbps_int32,
                gbps_fp64,
                gbps_long,
            )
        )

if __name__ == ""__main__"":
    main(sys.argv[1:])
```
Pull Request resolved: https://github.com/pytorch/pytorch/pull/102815
Approved by: https://github.com/ngimel, https://github.com/malfet",2023-06-02T22:33:24Z,Valentin Andrei
ae6dd20ba7725ea7c10d759f82891d86eb724c11,"[cuDNN V8 API] (reopen 2) Allow the number of kernels profiled under torch.backends.cudnn.benchmark = True to be limitedCudnnv8 benchmark limit (#78299)

Reopen of #77002 to address comments by @malfet

CC @ngimel @ptrblck
Pull Request resolved: https://github.com/pytorch/pytorch/pull/78299
Approved by: https://github.com/ngimel",2022-07-07T23:25:23Z,Eddie Yan
76abbbe3179a83b05629ac3010817c39ad102242,"Adding output_size to to_padded_tensor (#76640)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/76640

- Adding output_size argument to to_padded_tensor
- Modified add_padding_kernelLauncher and kernels to iterate over padded tensor batch size instead of nested tensor batch size
- No fast path for CPU version

Test Plan:
buck test mode/dev-nosan  //caffe2/test:nested

Performance test using N1763981:

{F728168808}

Reviewed By: cpuhrsch

Differential Revision: D36056902

fbshipit-source-id: d6df2939d6649128a7f43a2ef32d227870a8e583
(cherry picked from commit 09465f36f09d4d74c9b3303981d8cce0c7c1092a)",2022-05-03T18:17:59Z,Michael Anderson
d985cf46f18d202756dc9ce2ddbebd9507b29d43,Add workaround to fix include warnings in Python 2 builds. (#6716),2018-04-24T19:30:19Z,Zachary DeVito
6db3853eebb3b2d2e4c68adcb0d04d3259da5910,"Add doc for torch.cond (#108691)

We add a doc for torch.cond. This PR is a replacement of https://github.com/pytorch/pytorch/pull/107977.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/108691
Approved by: https://github.com/zou3519",2023-10-03T16:31:26Z,ydwu4
1d34f33d009d9d1e90ba913a6debbc0c518ab9ff,"Scale XBLOCK in triton reduction configs to avoid hitting max grid (#128826)

Scale XBLOCK size in triton_config_reduction to avoid hitting maxGridSize limits.

This issue was observed in gpt-fast examples with large sequence length:
Reproducer: https://gist.github.com/jataylo/8a0ba922fbf68e345d360a418b48b9f1

`RuntimeError: Triton Error [HIP]:  Code: 9, Messsage: invalid configuration argument`

Co-authored-by: Jason Ansel <<EMAIL>>
Pull Request resolved: https://github.com/pytorch/pytorch/pull/128826
Approved by: https://github.com/jansel, https://github.com/nmacchioni",2024-08-05T19:34:36Z,Jack Taylor
1dbbef6b4823b3eee441102fb250849e8efaec9d,"Fix crash in blob deallocation

Summary: We have to use copy constructor in Concat when copying non-primitive types

Reviewed By: Yangqing

Differential Revision: D6002883

fbshipit-source-id: 0aebc955079975bb6423291589ed09ce0660acf3",2017-10-11T01:53:13Z,Ilia Cherniavskii
d84173c025313143c20b8c7c5ffe8b2a24c74216,"[export] fix unlifting of custom class constants (#117979)

we didn't have a test covering this case, add one.

Aside: we should invest in actually unit testing the lifting/unlifting passes, both separately and also against each other. I have a diff cooking for that.

Differential Revision: [D52962180](https://our.internmc.facebook.com/intern/diff/D52962180/)
Pull Request resolved: https://github.com/pytorch/pytorch/pull/117979
Approved by: https://github.com/avikchaudhuri
ghstack dependencies: #115222, #117978",2024-01-22T19:14:47Z,suo
c51827b8ce956b166490c8e1540e538ddfb8a4d7,"[ez] Hash update to reuse issues again (#113961)

The bot that creates the issue got changed, but the search did not, so it wasn't finding old PRs and was just making new ones.

This PR makes it reuse PRs again instead of making a new one everytime.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/113961
Approved by: https://github.com/huydhn",2023-11-17T19:06:38Z,Catherine Lee
f684e44fd6ba99fb7497b9fc5e29b53504a02f11,"Revert ""Reduce pytest prints (#117069)""

This reverts commit 40dbd567e04483c671f9c897171bf9d1e7162b68.

Reverted https://github.com/pytorch/pytorch/pull/117069 on behalf of https://github.com/clee2000 due to need to handle timeout expired better ([comment](https://github.com/pytorch/pytorch/pull/117069#issuecomment-1901270953))",2024-01-19T23:07:51Z,PyTorch MergeBot
b56939dae117afd61937c78b92b0ad247dd27176,"Annotate more InstructionTranslator (#131680)

Pull Request resolved: https://github.com/pytorch/pytorch/pull/131680
Approved by: https://github.com/zou3519
ghstack dependencies: #131676",2024-07-24T18:25:03Z,Oguz Ulgen
a7fba7de22688f56f03a048676ff1404d417fb00,"Convert StoreTestUtils to Gtest (#43382)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/43382

StoreTestCommon defines standard helper functions that are used by all of our Store tests. These helpers currently throw exceptions upon failure, this PR changes them to use gtest assertions instead.
ghstack-source-id: 111690833

Test Plan: Tested the 2 PR's above this on devvm

Reviewed By: jiayisuse

Differential Revision: *********

fbshipit-source-id: 9e116cf2904e05ac0342a441e483501e00aad3dd",2020-09-10T00:13:36Z,Omkar Salpekar
209c6f9ab57f7c6b3d79ac69a23b2b38bfcfcf63,"Move device type init from BackendSelect to backend kernels (#37402)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/37402

Previously, BackendSelect kernels did just-in-time device type
initialization by calling `LegacyTypeDispatch.initForDispatchKey()`
with a computed dispatch key. Here we move the initialization to
the backend kernels themselves, where we can call the device-
specific initializer directly.

Putting this up to run tests on it, but a couple questions remain:
* why were only BackendSelect kernels doing this initialization?
  Not all factory ops appear there, nor are all the ops that do
  appear there factory ops. Currently we generate init code for
  exactly the BackendSelect ops, but the choice should be better
  motivated.
* the previous scheme maps HIP to its own legacy type dispatch
  entry, but the logic assumes it's exclusive with CUDA, and no
  ops appear to mention HIP explicitly, so the new logic doesn't
  expose a static entry point for it. Needs to be verified.

Test Plan: Imported from OSS

Differential Revision: D21282974

Pulled By: bhosmer

fbshipit-source-id: cd46eb788596948e0572a15fac0f8b43feca5d75",2020-05-05T01:42:28Z,Basil Hosmer
c4bf196334bea0579b4ce6083b292be8712c7561,"Strided masked reduction: mean (2nd try) (#67088)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/67088

Stack from [ghstack](https://github.com/ezyang/ghstack):
* __->__ #67088

Test Plan: Imported from OSS

Reviewed By: anjali411

Differential Revision: D32070264

Pulled By: cpuhrsch

fbshipit-source-id: 08a91550dd24fb0f51abf06591a0e26186c4f9f9",2021-11-01T23:10:03Z,Pearu Peterson
e05ee4c421a86eb9976346bb0e962a42ee7b9c52,"Remove BUILD_NAMEDTENSOR macros (#30894)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/30894

This PR begins the process of removing BUILD_NAMEDTENSOR macros. There
will be followups.

Reasons for removing the macros:
- BUILD_NAMEDTENSOR is always on and has been on since pytorch 1.3.0.
- Since we don't test building without it, it is useless to keep around.
- Code becomes nicer to read without the macros

Reasons for not removing the macros:
- potential for feature flagging

Now, I argue against needing to feature flag. The main reason why we
might want to feature flag is if we need to disable the feature.
We'd need a fast switch to disable the feature if someone discovers
in the future that named tensors caused some regression in some existing workflows.

In https://github.com/pytorch/pytorch/pull/25798, I did a variety of
macro- and micro- benchmarks to determine the performance impact of named
tensors on regular tensors.

[The
microbenchmarks](https://github.com/pytorch/pytorch/pull/25798#issuecomment-529014810)
were not very stable, and running the
microbenchmarks for more iterations doesn't actually help because the
noise is not distributed in a nice way. Instead of microbenchmarks I ran
a [profiler
(perf)](https://github.com/pytorch/pytorch/pull/25798#issuecomment-555707645)
to estimate how much overhead named tensors add to unnamed code. I
estimated the overhead to be less than 100ns for `add` and even smaller
for `mm`; there are ways to optimize even futher if we find this to be a
problem.

[Initial
macrobenchmarks](https://github.com/pytorch/pytorch/pull/25798#issuecomment-530539104)
were also not very stable. I ran imagenet for some number of epochs. To
make them more stable, I got rid of the data loading (which seemed to
vary between runs). [In some benchmarkers without data
loading](https://github.com/pytorch/pytorch/pull/25798#issuecomment-562214053),
we can see that the results are less noisy now. These results support
no noticeable regressions in speed.

Test Plan: - wait for CI

Differential Revision: D18858543

Pulled By: zou3519

fbshipit-source-id: 08bf3853a9f506c6b084808dc9ddd1e835f48c13",2019-12-10T15:49:14Z,Richard Zou
b3bce01e264f0022a106b16d7e5038a38a72bb4d,"Have add_video use NamedTemporaryFile directly (#20223)

Summary:
address comment in #16196
https://github.com/pytorch/pytorch/pull/16196/files#r278676986

cc orionr
Pull Request resolved: https://github.com/pytorch/pytorch/pull/20223

Reviewed By: natalialunova

Differential Revision: D15261528

Pulled By: orionr

fbshipit-source-id: 1aebcc6cb1c9313d890c5b506973855ebc63fb3b",2019-05-08T21:57:40Z,Tzu-Wei Huang
8cc57593b90f0196a54e5c08c7344d725ed5cd4e,"remove redundant trailing semicolons in StorageImpl.h (#97658)

remove redundant trailing semicolons in StorageImpl.h

Pull Request resolved: https://github.com/pytorch/pytorch/pull/97658
Approved by: https://github.com/kit1980, https://github.com/malfet",2023-04-25T21:04:22Z,mikey dagitses
ef156f913655c44056202ab43322f398bd7387aa,"Enable retry support for MPS tests (#94070)

Here is an example https://hud.pytorch.org/pytorch/pytorch/commit/d7c71a95b68dfd3b126acd021e05b18b5fa38f03 where the MPS test was flaky but not retried.  Thus it failed.  We probably would want to support retry on MPS tests like the rest of the CI
Pull Request resolved: https://github.com/pytorch/pytorch/pull/94070
Approved by: https://github.com/clee2000",2023-02-03T22:21:31Z,Huy Do
5ed5dfd915aa8988b6d30c3c424a31935f3a8002,"Don't run ios jobs on forks (#91112)

Fixes #ISSUE_NUMBER

Pull Request resolved: https://github.com/pytorch/pytorch/pull/91112
Approved by: https://github.com/huydhn",2022-12-20T19:13:11Z,clee2000
7bbd9befed0d64bb1c241cf4cdbfdd25486f12ce,"Improve example for ``torch.mode()`` (#115308)

Fixes #89820 and improves the documentation.

Co-authored-by: Sam Gross <<EMAIL>>
Pull Request resolved: https://github.com/pytorch/pytorch/pull/115308
Approved by: https://github.com/colesbury",2024-02-03T00:13:20Z,Linus
90040afc4494ca716ef2fa5e11552b973b05d8c0,Fix cwrap option filtering,2017-01-15T16:25:32Z,Adam Paszke
a40e0a7f2d235586237122e92561eef13169bb8f,"Add torch.version.git_version (#18299)

Summary:
Fixes: https://github.com/pytorch/pytorch/issues/18293
cc: colesbury
Pull Request resolved: https://github.com/pytorch/pytorch/pull/18299

Differential Revision: *********

Pulled By: soumith

fbshipit-source-id: cdb48ef37c8869713a9a43ea0da08e1bed9279a2",2019-03-26T02:54:27Z,"Gao, Xiang"
95d0b3199b2e0eb0516e439c8aa1a94b62113e1e,"Back out ""[ONNX] Fix an issue that optimizations might adjust graph inputs unexpectedly. (#61280)"" (#64004)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/64004

Pull Request resolved: https://github.com/pytorch/pytorch/pull/63904

Fixes *********

Test Plan: *********

Reviewed By: msaroufim

Differential Revision: *********

fbshipit-source-id: 6262901a78ca929cecda1cf740893139aa26f1b4",2021-08-26T19:48:01Z,Meghan Lele
ea4fbb2e5e646fd28c1bbfabe128ab2404a969c5,"[StaticRuntime] Replace hashtable based workspace with vector<IValue> (#45892)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/45892

Previously we were using hashtable (`std::unordered_map` in OSS, `folly::F14FastMap` in fb) for workspace, a container for all the IValues in the graph. Hashtable based lookups can be expensive. This diff replaces the hashtable with `std::vector` and extra bookkeepings are introduced to keep track of the indices of graph inputs/outputs in `StaticRuntime` and op inputs/outputs in `ProcessedNode`.

Reviewed By: dzhulgakov

Differential Revision: D24098763

fbshipit-source-id: 337f835ee144985029b5fa2ab98f9bcc5e3606b6",2020-10-08T16:48:37Z,Hao Lu
d6ff78fd00eab35435f2e0531e8a74f62d8071e9,"fix an over-indented return in trace_module

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/23358

Differential Revision: D16519010

Pulled By: Krovatkin

fbshipit-source-id: a7e4225b70e915d91c74874e3eca9bcb87baf84c",2019-07-29T18:11:44Z,Nikolay Korovaiko
9afdf017dc99a9d948b0daa754f77159ba6dcc4a,"Add force_on_cpu test to win cuda10.2 on GHA (#65094)

Summary:
Part of migrating from Circle.

Once we get a successful force_on_cpu test, we can move it to trunk only.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/65094

Reviewed By: seemethere

Differential Revision: D31086289

Pulled By: janeyx99

fbshipit-source-id: e1d135cc844d51f0b243b40efb49edca277d9de8",2021-09-21T18:08:44Z,Jane Xu
3fccc0446cc4f78f97e2b227a06e196ffee488d4,"Add dtensor and fsdp/2d tests to inductor_distributed CI (#114642)

Smuggle important and not too slow tests to run on this trunk job,
instead of just on the periodic job where they currently reside.
 - test_dtensor_compile took 70sec, test_fsdp_2d_parallel took 198sec
   locally

As a follow up, organize the distributed-mgpu tests better and maybe
rename this job to reflect its more 'general dist mgpu'

Pull Request resolved: https://github.com/pytorch/pytorch/pull/114642
Approved by: https://github.com/wanchaol, https://github.com/malfet",2023-11-27T23:30:03Z,Will Constable
438cc79f5a384a9508de5c462705f3cbb42c645b,"Improve more the error message with explicit recommendation

Following feedback.
The new message looks like:
```
# torch.nn.intrinsic.modules._FusedModule:
  - Is public: it is inside the module's (`torch.nn.intrinsic.modules`) `__all__`
  - Does NOT look public: because it starts with `_` (`_FusedModule`)
  - You can do either of these two things to fix this problem:
    - To make it NOT public: remove it from the modules's (`torch.nn.intrinsic.modules`) `__all__`
    - To make it look public: remove the `_` at the beginning of the name
# torch.ao.nn.sparse.quantized.dynamic.linear.LinearBlockSparsePattern:
  - Is public: it is an attribute that does not start with `_` on a module that does not have `__all__` defined
  - Does NOT look public: because its `__module__` attribute (`torch.ao.nn.sparse.quantized.utils`) is not within the torch library or does not start with the submodule where it is defined (`torch.ao.nn.sparse.quantized.dynamic.linear`)
  - You can do either of these two things to fix this problem:
    - To make it NOT public: either define a `__all__` for `torch.ao.nn.sparse.quantized.dynamic.linear` or add a `_` at the beginning of the name
    - To make it look public: make sure the `__module__` is properly set and points to a submodule of `torch.ao.nn.sparse.quantized.dynamic.linear`

```
Pull Request resolved: https://github.com/pytorch/pytorch/pull/76261
Approved by: https://github.com/NivekT",2022-04-25T13:59:55Z,Alban Desmaison
a5a1f0a6b14c1d5ae1088028065ab33b1f2ff5c7,"[executorch hash update] update the pinned executorch hash (#114996)

This PR is auto-generated nightly by [this action](https://github.com/pytorch/pytorch/blob/main/.github/workflows/_update-commit-hash.yml).
Update the pinned executorch hash.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/114996
Approved by: https://github.com/pytorchbot",2023-12-02T03:57:43Z,PyTorch UpdateBot
67416a2996349a2339328cac0f7e54c7d3b3c1d9,"[c10d] Introduce a util for detecting DMA connectivity among devices (#129510)

This PR introduces `_detect_dma_connectivity` - a utility for detecting DMA connectivity among devices.

The ""DMA connectivity"" in this context is more stringent than the ability to perform memory copy without CPU involvement. We define it as the ability for a device to issue load/store instructions and perform atomic operations on memory that resides on connected devices. The ability translates to the ability to run most aten GPU operations with operands backed by remote memory. `_detect_dma_connectivity` can help PyTorch and its users to determine whether certain DMA-based optimizations are possible.

`_detect_dma_connectivity` takes a `(device_type, connection_type)` pair and returns a matrix describing the connectivity. Connectivity detectors are statically registered on a `(device_type, connection_type)` basis. This PR implements the detector for `(CUDA, ""nvlink"")`. Later, detectors for pairs such as `(ROCM, ""infinity_fabric"")` can be introduced.

Example:

```python3
>>> from torch._C._autograd import DeviceType
>>> from torch._C._distributed_c10d import _detect_dma_connectivity
>>> connectivity = _detect_dma_connectivity(DeviceType.CUDA, ""nvlink"")
>>> for row in connectivity.matrix:
...     print(row)
...
[0, 18, 18, 18, 18, 18, 18, 18]
[18, 0, 18, 18, 18, 18, 18, 18]
[18, 18, 0, 18, 18, 18, 18, 18]
[18, 18, 18, 0, 18, 18, 18, 18]
[18, 18, 18, 18, 0, 18, 18, 18]
[18, 18, 18, 18, 18, 0, 18, 18]
[18, 18, 18, 18, 18, 18, 0, 18]
[18, 18, 18, 18, 18, 18, 18, 0]
```

Pull Request resolved: https://github.com/pytorch/pytorch/pull/129510
Approved by: https://github.com/weifengpy",2024-06-26T23:43:42Z,Yifu Wang
40d826074546558f6665a4c118335a7725503cac,"[ROCm] remove caffe2 from hipify (#137157)

- Remove all ""MasqueradingAsCUDA"" files and classes.
- Do not rename ""CUDA"" classes to ""HIP"".

Pull Request resolved: https://github.com/pytorch/pytorch/pull/137157
Approved by: https://github.com/eqy",2024-10-05T12:48:54Z,Jeff Daily
e6b361bd47ea719d6b3830a6898679d3d281dd38,"Refactor dynamo benchmark test script to reduce duplication (#96096)

Signed-off-by: Edward Z. Yang <<EMAIL>>

Pull Request resolved: https://github.com/pytorch/pytorch/pull/96096
Approved by: https://github.com/desertfire",2023-03-06T20:56:24Z,Edward Z. Yang
b0e2ed4d67f698ccb63d4e99ad1e82a6c8a4b68a,"removing some macros (#120314)

Summary: Will be making some changes in the surrounding code, they are going to be easier without macros

Differential Revision: D54001770

Pull Request resolved: https://github.com/pytorch/pytorch/pull/120314
Approved by: https://github.com/zhxchen17",2024-03-06T22:06:05Z,Denis Yaroshevskiy
ec48280afa495bba540d161b186bbd8b0e6c5cf4,"Improve error message when input is not in the right format (#25928)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/25928

Improved error message
ghstack-source-id: 89854172

Test Plan:
if given the input of wrong dimension, the message earlier
```
[QConv2D] each dimension of output tensor should be greater than 0
```
message now
```
Given groups=1, weight of size 20, 5, 5, 1, expected input (NHWC) 10, 1, 32, 32 to have 1 channels, but got 32 channels instead
```

Reviewed By: jianyuh

Differential Revision: D17287290

fbshipit-source-id: d91573d6d69f2a5e0e615ffbd47a0bd233636a0b",2019-09-11T20:31:59Z,Daya Khudia
b69c685c4a9f4d44427ebd1b4b45bbd7859e1430,"try to find cudnn header in /usr/include/cuda (#31755)

Summary:
With fedora negativo17 repo, the cudnn headers are installed in /usr/include/cuda directory, along side with other cuda libraries.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/31755

Differential Revision: D19697262

Pulled By: ezyang

fbshipit-source-id: be80d3467ffb90fd677d551f4403aea65a2ef5b3",2020-02-04T22:05:48Z,nihui
f5b68e74d75c38d5e2044fc6b62112181080bb3f,"Revert D25574962: [pytorch][PR] Updated derivative rules for complex svd and pinverse

Test Plan: revert-hammer

Differential Revision:
D25574962 (https://github.com/pytorch/pytorch/commit/9955355853a1c189a4a79209f82d39393b4be010)

Original commit changeset: 832b61303e88

fbshipit-source-id: d73f77f3e51b0f535dad6d21c5bebf8d41a6bfbd",2020-12-17T08:58:25Z,Mike Ruberry
cf811d2fb365fa7f8543dcb5f71863c1a812f180,"retain undefined tensors in backward pass (#41490)

Summary:
Leave undefined tensors / None returned from custom backward functions as undefined/None instead of creating a tensor full of zeros. This change improves performance in some cases.

**This is BC-Breaking:** Custom backward functions that return None will now see it potentially being propagated all the way up to AccumulateGrad nodes. Potential impact is that .grad field of leaf tensors as well as the result of autograd.grad may be undefined/None where it used to be a tensor full of zeros. Also, autograd.grad may raise an error, if so, consider using allow_unused=True ([see doc](https://pytorch.org/docs/stable/autograd.html?highlight=autograd%20grad#torch.autograd.grad)) if it applies to your case.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/41490

Reviewed By: albanD

Differential Revision: D22578241

Pulled By: heitorschueroff

fbshipit-source-id: f4966f4cb520069294f8c5c1691eeea799cc0abe",2020-07-17T19:41:06Z,Heitor Schueroff de Souza
6c56e1ce2b8d850eb8f51731ecc8be415160e02b,"Use fmt::format in NCCLUtils and ProcessGroupNCCL instead of c10::str (#107268)

Fixes #64604

Pull Request resolved: https://github.com/pytorch/pytorch/pull/107268
Approved by: https://github.com/fduwjj",2023-10-20T05:26:47Z,Andrei Gheorghe
4cb73f5a4c841ffa0f20a27d920173e16549c4f7,"Allow for string literal return during symbolic tracing (#47618)

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/47618

Test Plan: Imported from OSS

Reviewed By: jamesr66a

Differential Revision: D24870422

Pulled By: ansley

fbshipit-source-id: 41c56c2f4f1f7bb360cea0fb346f6e4d495f5c2b",2020-11-11T16:52:06Z,Ansley Ussery
0e2330d84ce13ac4b92ce75c5dc9141ac7d25b92,"fix lint (#119395)

Summary: as title

Test Plan: lint

Differential Revision: D53532399

Pull Request resolved: https://github.com/pytorch/pytorch/pull/119395
Approved by: https://github.com/tugsbayasgalan, https://github.com/malfet",2024-02-07T19:06:41Z,Michael Suo
855b7e28ee24c2af98557f4dc1952b912bfc32df,"START_IND & END_IND macros, removed unnecessary computation in updateGradInput",2017-09-20T17:19:19Z,SsnL
f3bf46e801dec2637751224fd6e27fbf97453bc6,"enable bf16 emb (#94163)

Merge https://github.com/pytorch/pytorch/pull/89199 and https://github.com/pytorch/pytorch/pull/91949 into one PR.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/94163
Approved by: https://github.com/jianyuh, https://github.com/malfet, https://github.com/jgong5",2023-02-06T07:11:37Z,haozhe.zhu
7ab6f56ca72a5f1b8c7b0c73e3947c0af3f998c8,"[quant][core] Add quantize/dequantize ops for decomposed quantized Tensor representation (#87093)

Summary:
Added q/dq implementation for out of core (decomposed) quantized Tensor representation, meaning that
instead of storing quantization parameters (e.g. scale/zero_point) in a separate quantized Tensor object, we will store
quantization parameters in the argument of operators.
```
quantize(float32_tensor, scale, zero_point, dtype) -> int8_tensor
dequantize(int8_tensor, scale, zero_point, dtype) -> float32_tensor
```

Test Plan:
python test/test_quantization.py TestQuantizedTensor.test_decomposed_quantize
python test/test_quantization.py TestQuantizedTensor.test_decomposed_dequantize

Reviewers:

Subscribers:

Tasks:

Tags:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/87093
Approved by: https://github.com/dzdang, https://github.com/z-a-f",2022-10-25T17:39:24Z,Jerry Zhang
bf1b8adee661ae72bd294bc3ab98fb8e1de8eff0,"Turn static inline into static function (#139843)

Fixes #ISSUE_NUMBER

Pull Request resolved: https://github.com/pytorch/pytorch/pull/139843
Approved by: https://github.com/ezyang",2024-11-07T23:58:18Z,cyy
43ea782af37ffbcd3c583b0f63cc2817c19429f7,"Multiprocessing support for NT (#110292)

Fixes #110161

Allows NTs to be used in DataLoaders with `num_workers > 1`.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/110292
Approved by: https://github.com/cpuhrsch, https://github.com/albanD",2023-10-10T18:22:52Z,Joel Schlosser
4307ccde99fbec3afcd7cbee1a0c2db0070187cc,"Move ONNX's TorchModelType to pytorch_test_common to fix circ. dep. (#115353)

Pull Request resolved: https://github.com/pytorch/pytorch/pull/115353
Approved by: https://github.com/BowenBao",2023-12-11T19:14:53Z,Thiago Crepaldi
97509c8eb2aef89c8bf8429018aa6ce4a8269fde,"Revert ""[Inductor][Quant] Fix PT2E Dynamic Quant regression (#125207)""

This reverts commit 3da949b0fbe91e802d30e00165141d1390621d71.

Reverted https://github.com/pytorch/pytorch/pull/125207 on behalf of https://github.com/huydhn due to Sorry for reverting your change but I think there is a land race with the change https://hud.pytorch.org/pytorch/pytorch/commit/33e6791645b5950b0f39301f55b8a4a79c0ca847 ([comment](https://github.com/pytorch/pytorch/pull/124041#issuecomment-2101766558))",2024-05-09T01:34:19Z,PyTorch MergeBot
711be82951739e8c3cf7e5d2f81d22e93451fa11,"Make optimize a thread_local flag

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/23170

Test Plan: Imported from OSS

Differential Revision: D16441912

Pulled By: suo

fbshipit-source-id: a33485178a329d54e41e364c4f14950f88481c55",2019-07-25T06:05:48Z,Michael Suo
f43351718165579d908150e785815d1e65d439ab,"[dynamo][decorator] Support disable on nn modules (#124185)

Fixes https://github.com/pytorch/pytorch/issues/123979

Pull Request resolved: https://github.com/pytorch/pytorch/pull/124185
Approved by: https://github.com/weifengpy, https://github.com/yoyoyocmu",2024-04-16T16:24:14Z,Animesh Jain
bdd9ef19815520f0680b6218a21045fdb5894da0,"Support RowWiseSparseAdam on GPU (#35404)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/35404

Implement RowWiseSparseAdam on CUDA

Reviewed By: xw285cornell

Differential Revision: D20650225

fbshipit-source-id: 5f871e2f259e362b713c9281b4d94534453995cf",2020-07-31T17:41:57Z,Yan Xie
827a00cf63df1ddcf1c776713925c0c1ee0d7c2b,"Support interface python assignment as an attribute (#26734)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/26734

This PR added the python assignment for interface as an attribute in the
module, it enables any object that implicitly inheriting the specific
interface to be able to be assigned to the interface type in python.

Serialization support for interface/class assignment will be done in the
follow up PR

Test Plan: Imported from OSS

Differential Revision: D17742708

Pulled By: wanchaol

fbshipit-source-id: a0a2d8c74b60ed3fa6c05e1b0d49b7ad1abc670b",2019-10-04T00:13:02Z,Wanchao Liang
6cc15c1a22fb98709d930585aa5781799f9638db,"Simplify typeid SFINAE (#12706)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/12706

If both branches are valid C++ code independent from the type passed in, then we can just use if/else inside of a constexpr function
to decide between the cases. Only if one branch would be invalid code (say because type T doesn't have a default constructor), we'd
need ""constexpr if"" or SFINAE.

Reviewed By: ezyang

Differential Revision: D10400927

fbshipit-source-id: 16d9855913af960b68ee406388d6b9021bfeb34a",2018-10-22T18:22:56Z,Sebastian Messmer
c0c43aee9c967921f4686dbefea53a6143a3ddcc,"Ignore cudaErrorPeerAccessAlreadyEnabled.

If we call cudaDeviceEnablePeerAccess for the same devices twice, it
will return cudaErrorPeerAccessAlreadyEnabled. THCudaCheck treats that
as an error and fails, whereas it really only indicates that peer access
has already been enabled. In which case we shouldn't fail.",2015-02-05T12:10:19Z,Dominik Grewe
c06dfd7c26102ac2436ca25609c92fa794e972ca,"[fx2trt] Check input device in TRTModule (#63893)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/63893

Add a check to ensure all the inputs are on cuda device.

Test Plan: CI

Reviewed By: kflu, houseroad

Differential Revision: D30525265

fbshipit-source-id: 6e50b70fd535defc1f802d51e8bb991b2dd73741",2021-08-25T17:22:17Z,Shiyan Deng
75ce0406207d0a6db82fed93b3a9893948e7796c,"[TensorExpr] Allow for 'keepdim' argument in aten::mean in NNC's external call. (#68756)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/68756

That fixes some warnings in our tests.

Test Plan: Imported from OSS

Reviewed By: navahgar

Differential Revision: D32600952

Pulled By: ZolotukhinM

fbshipit-source-id: 548eaf3659e20795cce44d8f57e77f4a47d44d98",2021-11-30T08:03:21Z,Mikhail Zolotukhin
5c79046d39a0d585744b8c195bc3500a657707f7,Use persistent tensor to store exp_inf (part of optimizer's state) (#1152),2017-03-31T14:30:31Z,Tudor Berariu
76b290344f917ee0b9e1c69863ae04354a298dd2,"Defer setting capturable in optimizer variable (#123497)

Pull Request resolved: https://github.com/pytorch/pytorch/pull/123497
Approved by: https://github.com/anijain2305
ghstack dependencies: #123496",2024-04-06T02:52:51Z,Michael Lazos
975ff6624b098171c6ce8222fbb0cd407b5b5f26,"DOC: backport doc build fix from 1.7, tweak link (#47349)

Summary:
xref gh-46927 to the 1.7 release branch

This backports a fix to the script to push docs to pytorch/pytorch.github.io. Specifically, it pushes to the correct directory when a tag is created here. This issue became apparent in the 1.7 release cycle and should be backported to here.

Along the way, fix the canonical link to the pytorch/audio documentation now that they use subdirectories for the versions, xref pytorch/audio#992. This saves a redirect.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/47349

Reviewed By: zhangguanheng66

Differential Revision: *********

Pulled By: seemethere

fbshipit-source-id: c778c94a05f1c3e916217bb184f69107e7d2c098",2020-11-19T17:48:49Z,mattip
d8732b3b43983dd16d8f3c4bfd3970adc4c03b89,"Gradle build with offline dependencies (#29262)

Summary:
https://github.com/pytorch/pytorch/issues/29159

Introducing GRADLE_OFFLINE environment variable to use '--offline' gradle argument which will only use local gradle cache without network.

As it is cache and has some expiration logic - before every start of gradle 'touch' files to update last access time.

Deploying new docker images that includes prefetching to gradle cache all android dependencies, commit with update of docker images: https://github.com/pytorch/pytorch-ci-dockerfiles/commit/df07dd56812a42105e04b0ba4267912e1bf0834e

Reenable android gradle jobs on CI (revert of https://github.com/pytorch/pytorch/pull/29606/commits/54e6a7eede660b953c27e566eb4a9cdcab86b25d)
Pull Request resolved: https://github.com/pytorch/pytorch/pull/29262

Differential Revision: *********

Pulled By: IvanKobzarev

fbshipit-source-id: 8fb0b54fd94e13b3144af2e345c6b00b258dcc0f",2019-11-13T06:46:38Z,Ivan Kobzarev
136dadd689981a334985f2029f6d3e747c36da5c,"fix norrow_copy correctness issue for non-contiguous input for cpu path (#91789)

Fix https://github.com/pytorch/pytorch/issues/91690.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/91789
Approved by: https://github.com/jgong5, https://github.com/lezcano",2023-01-06T03:48:38Z,XiaobingSuper
c5bee1ec4f261f3e250ea3ee974a0e13fb79de3b,"[PyTorch] Parallelize gelu via tensoriterator (#58950)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/58950

Use tensor iterator's API to set grain size in order to parallelize gelu op.
ghstack-source-id: 130947174

Test Plan: test_gelu

Reviewed By: ezyang

Differential Revision: D28689819

fbshipit-source-id: 0a02066d47a4d9648323c5ec27d7e0e91f4c303a",2021-06-09T23:07:12Z,Kimish Patel
6e86a40694f03c66680201c01c13347eff38a951,"Revert ""[Dynamo] Check for __bool__ attribute before accessing it (#120943)""

This reverts commit dd7aeedb72f8a96d0f168308292e0d41c095f01b.

Reverted https://github.com/pytorch/pytorch/pull/120943 on behalf of https://github.com/DanilBaibak due to Broken trunk ([comment](https://github.com/pytorch/pytorch/pull/120943#issuecomment-2063098295))",2024-04-18T06:34:32Z,PyTorch MergeBot
c6e9e9359f5538dfde3a6c4b0ed3c0cb2045b59b,"[Codemod][GleanFbcode] Remove dead includes in caffe2/test (#39023)

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/39023

Reviewed By: orionr

Differential Revision: D21702529

fbshipit-source-id: 6945bba95609102409850b105a8a091e33b8acc9",2020-05-27T21:00:34Z,Nikita Shulga
7c2290e7cee76591c1990fa60e4c4c223ab71012,"Better error when module attr is used (#18164)

Summary:
Adds a suggestion to add to __constants__ when a torch.nn.Module attr is accessed
Pull Request resolved: https://github.com/pytorch/pytorch/pull/18164

Differential Revision: D14580060

Pulled By: eellison

fbshipit-source-id: 0c5adc21d7341a5691d4b45930947cb1ba84c8e8",2019-03-23T03:13:02Z,Elias Ellison
183c2071f9b814cad2766ad43089ad8cd0e7259e,"Generate wrap_dim code on derived type rather than base type.
Either should work, but code feels more natural this way.",2017-09-06T19:05:34Z,Gregory Chanan
3d34afa9ae035b4263ee0381fbdce2e3858993ae,"Revert ""Removed python dispatch keys from dispatch key extraction""

This reverts commit 3c1dd4e752162db1d7cc993187da623cec3f0d2b.

Reverted https://github.com/pytorch/pytorch/pull/74971 on behalf of https://github.com/atalman",2022-03-31T21:44:56Z,PyTorch MergeBot
0ae0fac1bb9eb82671d0a70fb76602db3670b3a0,"Clarify, make consistent, and test the behavior of logspace when dtype is integral (#47647)

Summary:
torch.logspace doesn't seem to have explained how integers are handled.
Add some clarification and some test when dtype is integral.

The CUDA implementation is also updated to be consistent with CPU implementation.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/47647

Reviewed By: gchanan

Differential Revision: *********

Pulled By: walterddr

fbshipit-source-id: 45237574d04c56992c18766667ff1ed71be77ac3",2021-01-15T20:27:03Z,Hong Xu
92d0700520b80d768bab199fa60c7883f0ef9c3d,"ci: Switch MPS tests to self hosted runners on AWS (#81772)

Signed-off-by: Eli Uriegas <<EMAIL>>
Pull Request resolved: https://github.com/pytorch/pytorch/pull/81772
Approved by: https://github.com/janeyx99",2022-07-20T16:47:48Z,Eli Uriegas
53b4f6c0f61141ec85a74d9dbf584a22af826e48,"Revert ""[jit] Add c++ stacktraces for jit::ErrorReport (#94842)"" (#95886)

This reverts commit 70029214f300f611e7dd816b5f64426224f6ab96.

It broke some internal tests.

Differential Revision: [*********](https://our.internmc.facebook.com/intern/diff/*********)
Pull Request resolved: https://github.com/pytorch/pytorch/pull/95886
Approved by: https://github.com/malfet, https://github.com/qihqi",2023-03-02T15:38:51Z,David Berard
61db8b64ec45d43144deefd9525d1c2cc716cc83,"Build option USE_NUMA should only show up on Linux. (#23673)

Summary:
(intentionally left blank)
Pull Request resolved: https://github.com/pytorch/pytorch/pull/23673

Differential Revision: D16627453

Pulled By: vincentqb

fbshipit-source-id: df62f1b26901bec6369b5589b98124165f40e6f1",2019-08-09T15:10:22Z,Hong Xu
74c3dcd1d2ba6de0e7fdd1969e1a09333e375c84,"Revert D23725053: [pytorch][PR] change self.generator to generator

Test Plan: revert-hammer

Differential Revision:
D23725053 (https://github.com/pytorch/pytorch/commit/a011b86115541365ebd55598f85ff9a42a6875d8)

Original commit changeset: 89706313013d

fbshipit-source-id: 035214f0d4298d29a52f8032d364b52dfd956fe8",2020-09-17T16:38:32Z,Natalia Gimelshein
eaf9b28c55322336754e824de61490af5ce6166b,"[quantization] Use torchbind for Linear PackedParams (#34140)

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/34140

Test Plan: Imported from OSS

Reviewed By: ZolotukhinM

Differential Revision: D20229168

Pulled By: jamesr66a

fbshipit-source-id: 3607cac9aa5b4b044572329742baed03350491c6",2020-05-08T02:01:19Z,James Reed
17567e5b29a77dee137dfc365e9ed3f1cf358950,"[pytorch@arvr/windows] Fix pytorch build/import on Windows @ ovrsource (#97193)

Summary:

- Importing torch on Windows can cause a crash within python.
- The problem was introduced by the change in `Module.cpp` from https://github.com/pytorch/pytorch/pull/94927
- The cause is that a call to `PyObject* initModule(void)` declared with a `__declspec(dllimport)` specifier can lead to a crash if the definition doesn't include the `__declspec(dllexport)` counterpart.
- To mitigate the problem without introducing  customized macros and changing the build system (note, `#include <c10/macros/Export.h>` doesn't work in `stub.c`) is to simply remove the `__declspec(dllimport)` specifier.
- According to https://web.archive.org/web/20140808231508/http://blogs.msdn.com/b/russellk/archive/2005/03/20/399465.aspx and other sources, `__declspec(dllimport)` only leads to some code optimizations, and since `initModule()` is only called once at startup, this is marginal.
- Note: the `stub_with_flatbuffer.c` file counterpart wasn't affected, therefore, not touched.

Differential Revision: *********

Pull Request resolved: https://github.com/pytorch/pytorch/pull/97193
Approved by: https://github.com/ezyang",2023-03-24T18:32:40Z,Eric Sauser
4ff8cd8f3a84358cb614bc870c8c47f2fa779893,"[pytorch][codegen] gen_python_functions.py loading native_functions.yaml / deprecated.yaml directly (#47746)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/47746

- Removed the integration hack in gen_python_functions.py. It now directly
  loads native_functions.yaml. All dependencies on Declarations.yaml
  have been removed / moved to elsewhere.
- Rewrote the deprecated.yaml parsing logic to work with new data model directly.

Confirmed byte-for-byte compatible with the old codegen:
```
Run it before and after this PR:
  .jenkins/pytorch/codegen-test.sh <baseline_output_dir>
  .jenkins/pytorch/codegen-test.sh <test_output_dir>

Then run diff to compare the generated files:
  diff -Naur <baseline_output_dir> <test_output_dir>
```

Differential Revision: D24885067

Test Plan: Imported from OSS

Reviewed By: bhosmer

Pulled By: ljk53

fbshipit-source-id: 8e906b7dd36a64395087bd290f6f54596485ceb4",2020-11-14T10:22:37Z,Jiakai Liu
4b26cafb8fa7eef7cbfdc0327f85f30e0a38e8ec,"make validate debug-only in Device copy ctr (#47854)

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/47854

Test Plan: Imported from OSS

Reviewed By: ezyang

Differential Revision: D25003113

Pulled By: bdhirsh

fbshipit-source-id: e17e6495db65c48c7daf3429acbd86742286a1f3",2020-12-09T16:09:08Z,Brian Hirsh
3f4b46ac9e1a3d88964be357675aa1225aa82aad,Add potrs with MAGMA,2016-03-13T01:54:23Z,Brandon Amos
85121a7a0f24faccdb36093e92262c186fc65ee0,"Added CUDA support for complex input for torch.cholesky_solve (#47047)

Summary:
`torch.cholesky_solve` now works for complex inputs on GPU.
I moved the existing tests to `test_linalg.py` and modified them to test complex and float32 dtypes.
Differentiation also works correctly with complex inputs now.

Ref. https://github.com/pytorch/pytorch/issues/33152

Pull Request resolved: https://github.com/pytorch/pytorch/pull/47047

Reviewed By: ngimel

Differential Revision: D24730020

Pulled By: mruberry

fbshipit-source-id: 95402da5789c56e5a682019790985207fa28fa1f",2020-12-06T04:16:50Z,Ivan Yashchuk
74a5d62d7ca9204b3b24137065c73fc7c66cc02d,"NCCL process group: avoid workEnqueue when capturing cuda graph (#102542)

Summary:
In torch.distributed, we make ProcessGroupNCCL not call workEnqueue when the cuda stream is capturing. I.e., when capturing a CUDA graph, we do not enqueue anything for the watchdog thread to consider. This allows capturing NCCL operations in a CUDA Graph.

This is followup to an internal discussion [1] where the watchdog thread was observed to crash when using cuda graphs containing an all_reduce. The watchdog thread wants to query events pertaining to enqueued work items, but this can't be done for ""events"" created during cuda graph capture.

[1] https://fb.workplace.com/groups/1405155842844877/posts/6975201909173548/

Test Plan: Test added. Also, the repro mentioned in https://fb.workplace.com/groups/1405155842844877/posts/7003002339726838/ runs successfully after this change.

Differential Revision: D46274814

Pull Request resolved: https://github.com/pytorch/pytorch/pull/102542
Approved by: https://github.com/kwen2501",2023-06-09T18:15:58Z,Jeremy Reizenstein
e7293ee5ae7195cff24d7166e2bf1378e0e02693,[functorch] skip flaky(?) test,2022-04-23T02:15:47Z,Horace He
237c27c35f368e419c805a445e36aaf50f8c74d2,Fix reduction functions not respecting the strides of output when output is correct size (#4995),2018-02-06T15:50:28Z,Richard Zou
7f66fa62ca31cc1b5178c532cb152288e55a7c3c,"Fix typing errors in torch.distributed.nn.* directory. (#47533)

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/47533

Test Plan: Imported from OSS

Reviewed By: walterddr

Differential Revision: D24952500

Pulled By: xuzhao9

fbshipit-source-id: 8e66784fd8f9f111b6329e0bb48d6cd61c690a4a",2020-11-17T07:16:02Z,Xu Zhao
8bcfb30d9734ef218135e716e4ae2c81fd2de19f,make android,2015-12-19T06:42:29Z,Yangqing Jia
89a6680036ee4371caad48e753d3d5e2ec4604ea,"Update the pull request template (#81991)

### Description
Makes the pull request template more useful by adding a couple concrete sections which should usually be filled out

Pull Request resolved: https://github.com/pytorch/pytorch/pull/81991
Approved by: https://github.com/huydhn",2022-07-22T19:29:18Z,Zain Rizvi
3187a71bbe0689c1f81d3ac1f85c5ddcdaaeeb72,"[test] vc toolchain modification (#54589)

Summary:
Fixes https://github.com/pytorch/pytorch/issues/54502
Needs to be merged after https://github.com/pytorch/builder/pull/684

Pull Request resolved: https://github.com/pytorch/pytorch/pull/54589

Reviewed By: walterddr

Differential Revision: D27402066

Pulled By: seemethere

fbshipit-source-id: 68f92485d89edf2c3315de8c57447f180679c77d",2021-03-29T18:17:52Z,peter
2e2a74670dc231078666f8fbc16b63766ebe480b,"torch.sparse.softmax: allow negative dim (#102172)

Pull Request resolved: https://github.com/pytorch/pytorch/pull/102172
Approved by: https://github.com/cpuhrsch",2023-05-24T16:17:24Z,Nikita Vedeneev
d1f9c03cef90f1bac64e66dfeb2a185d85f24739,"Use `const auto` with irange (#62990)

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/62990

Test Plan: Sandcastle

Reviewed By: zhouzhuojie

Differential Revision: D30199748

fbshipit-source-id: 284b208ffa3c6c4749e5ac9b1fccb28914590f2c",2021-08-11T00:57:22Z,Richard Barnes
dc43ad428603539a2051940c09b191825f66203d,"add is_grad_enabled check in runtime_wrapper before running with torch.no_grad (#117089)

We observed that `with torch.no_grad()` in runtime_wrapper introduced ~10% (0.06ms->0.066ms) inference performance regression on lennard_jones on cpu.
For inference tasks in benchmark, grad has been disabled, but in the current runtime_wrapper, no_grad is set again and its time is counted into the running time.
Therefore, we add `is_grad_enabled` check in runtime_wrapper before running with torch.no_grad. If grad has been disabled, there is no need to set no_grad.

Before this pr:
1.043x
dev,name,batch_size,speedup,abs_latency,compilation_latency,compression_ratio,eager_peak_mem,dynamo_peak_mem,calls_captured,unique_graphs,graph_breaks,unique_graph_breaks
cpu,lennard_jones,1,**1.043427**,**0.068366**,4.756151,0.941846,45.056819,47.838822,9,1,0,0

After this pr:
1.146x
dev,name,batch_size,speedup,abs_latency,compilation_latency,compression_ratio,eager_peak_mem,dynamo_peak_mem,calls_captured,unique_graphs,graph_breaks,unique_graph_breaks
cpu,lennard_jones,1,**1.146190**,**0.061844**,4.468380,0.936456,44.427264,47.441920,9,1,0,0

Pull Request resolved: https://github.com/pytorch/pytorch/pull/117089
Approved by: https://github.com/jgong5, https://github.com/bdhirsh",2024-01-10T03:37:09Z,blzheng
3339da30df298c54030bb7fdb4364db6746f9159,[functorch] Fix CI,2021-12-20T04:05:14Z,Richard Zou
564296f05166812f501ee19dcf24af50e7bd7e08,"[2/3] [JIT] Make sure fusion occurs in test_tensorexpr (#45789)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/45789

Making sure that more tests invoke a run with a Fusion Group.

Test Plan: Imported from OSS

Reviewed By: Krovatkin

Differential Revision: D24169535

Pulled By: eellison

fbshipit-source-id: 54d7af434772ba52144b12d15d32ae30460c0c3c",2020-10-08T18:59:57Z,Elias Ellison
a1e63251497feaddea3d26cbc9ec2093993d2b24,"Implement linear module for APoT quantization (#82105)

### Summary
Implement linear module to support APoT quantization. Use bitshifting method discussed in APoT paper https://arxiv.org/pdf/1909.13144.pdf to multiply PoT terms in APoT quantized weight tensor with uniformly quantized activation tensor to demonstrate alternative to matrix multiplication.

Multiplication using bitshifting for PoT:

<img width=""340"" alt=""Screen Shot 2022-07-25 at 12 44 26 PM"" src=""https://user-images.githubusercontent.com/68875504/180831050-ff849bca-8eb0-4b69-9b7f-c6c94a4cdfb5.png"">

### Test Plan
Run unit tests with: `python /pytorch/test/quantization/core/experimental/test_linear.py`
Pull Request resolved: https://github.com/pytorch/pytorch/pull/82105
Approved by: https://github.com/HDCharles",2022-07-28T03:57:30Z,asl3
49b69b2adeb230a15ed27ee2d25bb44835769681,"[JIT] fix broadcasting lists of ints (#39481)

Summary:
Previously, on conversion from python -> c++ it was casted to double list through bad copy pasta. It's pretty unusual for someone to script a broadcasting list function directly since it's an internal api, so it was unlikely to affect anyone.

Fix for https://github.com/pytorch/pytorch/issues/39450
Pull Request resolved: https://github.com/pytorch/pytorch/pull/39481

Reviewed By: jamesr66a

Differential Revision: D21870557

Pulled By: eellison

fbshipit-source-id: e704e5e87d2702a270b7d65c4df444246a134480",2020-06-04T19:13:33Z,Elias Ellison
57e52393213b6b4fba3b334654b96396a2904087,"Introduce Tensor overload to linspace and logspace (#104889)

Pull Request resolved: https://github.com/pytorch/pytorch/pull/104889
Approved by: https://github.com/zou3519
ghstack dependencies: #107958",2023-09-07T13:53:39Z,Li-Huai (Allan) Lin
294f9d12826d63df1a736ffd4ef9dab2af2d10d0,"[Profiler][Minor] Organize collection.h/.cpp (#82992)

Collection of Torch ops is quite complex compared to backend events / allocations / ooms. Python is also complex, however it is already factored into a standalone unit. This PR just shuffles the contents of collection.cpp to group the Torch op specific parts together, and does various cleanups to the code.

Differential Revision: [D38426344](https://our.internmc.facebook.com/intern/diff/D38426344/)
Pull Request resolved: https://github.com/pytorch/pytorch/pull/82992
Approved by: https://github.com/chaekit",2022-08-20T01:42:34Z,Taylor Robie
b35f70da0555650bb3b358753ea615ba5ecba6df,"[ez] fixup the export of D62879819 (#136900)

a line from D62879819 (#136190) went missing somehow
Pull Request resolved: https://github.com/pytorch/pytorch/pull/136900
Approved by: https://github.com/atalman",2024-09-28T13:46:17Z,Ivan Zaitsev
72a7351993c953500bd8cdb1fb7a9e33aaa7ef9d,"Pin linux ninja dep to 1.10.2 (#88548)

The latest version 1.11.1 breaks PyTorch CI.  A bunch of tests are failing now in master https://hud.pytorch.org/pytorch/pytorch/commit/d1ee0730410ac910760c0a21156e574093a0d15a.  Curiously, the latest commit https://hud.pytorch.org/pytorch/pytorch/commit/81042d3a53335259c60e5aa8c9b9614c3d87b05f looks green, but it's good to pin this dependency anyway

https://github.com/pytorch/pytorch/blob/master/.circleci/docker/requirements-ci.txt#L95-L97 has a curious note about ninja and why it's not part of the docker container (need to revisit this later on):

```
#ninja
#Description: build system.  Note that it install from
#here breaks things so it is commented out
```

This is one more reason to justify the effort to consolidating all pip and conda dependencies to get rid of this family of issue.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/88548
Approved by: https://github.com/clee2000",2022-11-07T23:53:17Z,Huy Do
1114b051222c3478aa3f0140802e4f4f3147f994,"Updating submodules

Summary:
GitHub commits:

https://github.com/facebook/rocksdb/commit/97631357aa274d06a7ab09b3cde7b909262cc4dd
https://github.com/pytorch/fbgemm/commit/2f1477dfee9465c1e2dbdf21722970b3fa1baf86

Test Plan: n/a

Reviewed By: 2d2d2d2d2d

fbshipit-source-id: 33029d2e8c6a3664a35823829670f6ed9dfc3b44",2019-09-13T22:07:43Z,svcscm
a036f9a65f713c144b97c902cf9773cf8f21142e,"Create README.md of caffe2/quantization/server

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/14217

Reviewed By: csummersea

Differential Revision: *********

Pulled By: jspark1105

fbshipit-source-id: bddf4f1c2dc5ec8ea6ebe9e265956f367e082d52",2018-11-20T05:44:29Z,Jongsoo Park
9f519d2d2d5dd4490de43ec2bcea59efab13e225,"Simplify benchmark patterns in mypy-strict.ini (#55700)

Summary:
These two lines were added in https://github.com/pytorch/pytorch/issues/53296, but they are needlessly complicated; this PR consolidates them.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/55700

Test Plan:
Run this command, and verify that the same number of files is given both before and after this PR:
```
mypy --config=mypy-strict.ini
```

Reviewed By: robieta

Differential Revision: *********

Pulled By: samestep

fbshipit-source-id: a34968cdff29cb8ad83813b277114224b5e37569",2021-04-09T21:47:24Z,Sam Estep
f9d32c4fa8f5990ac09ba0f9a1298d0348433179,"[JIT] Add selective backend lowering API (#43613)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/43613

**Summary**
This commit adds a helper/utility to faciliate the selective lowering of
specific submodules within a module hierarchy to a JIT backend. The reason
that this is needed is that lowering a submodule of a scripted
module to a backend after the module has been scripted requires
adjusting its JIT type.

**Test Plan**
This commit refactors `NestedModuleTest` in `jit/test_backends.py` to
use this new selective lowering API.

**Fixes**
This commit fixes ##41432.

Test Plan: Imported from OSS

Reviewed By: mortzur

Differential Revision: D23339855

Pulled By: SplitInfinity

fbshipit-source-id: d9e69aa502febbe04fd41558c70d219729252be9",2020-10-30T07:36:13Z,Meghan Lele
c8d1ec02be475764e6a62a4e0d0c7d09a9d79fcf,"[jit] Have ScriptModule inherit from Module (#5769)

* Have ScriptModule inherit from Module
  This is accomplished by created replacement _parameters, _buffers,
  and _modules which implement the OrderedDict APIs but which
  actually get/set their members inside script::Module
* Merge TracedModule with ScriptModule
* Move logic of attribute handling into Python bindings rather than
  make script::Module handle it. This was redundant with nn.Module,
  which already handles attribute.
* Make TracedModule a subclass of ScriptModule
* Move handling of attribute kind logic into bindings.
* Allow ScriptModule to contain non-script module submodules.",2018-03-22T04:17:49Z,Zachary DeVito
38c97fb6f0725fa0c02687023b101f0f8339cca7,"[shape inference] add shape inference support

Summary:
* To make pruning op compatible with shape inference, we introduced a new quantile argument (as in *********) to differentiate dynamic/fixed pruning.

* The fixed pruning op has defined output shapes. However, the input shapes are not determined therefore we want to bypass the input shapes checking for two pruning ops, as implemented in this diff.

Test Plan:
buck test caffe2/caffe2/opt:bound_shape_inference_test

```
Started reporting to test run: https://our.intern.facebook.com/intern/testinfra/testrun/844425102187909
    ✓ ListingSuccess: caffe2/caffe2/opt:bound_shape_inference_test - main (1.973)
    ✓ Pass: caffe2/caffe2/opt:bound_shape_inference_test - BoundShapeInference.FC3D (2.604)
    ✓ Pass: caffe2/caffe2/opt:bound_shape_inference_test - BoundShapeInference.SparseLengthsSumFused4BitRowwise (2.635)
    ✓ Pass: caffe2/caffe2/opt:bound_shape_inference_test - BoundShapeInference.FC (2.690)
    ✓ Pass: caffe2/caffe2/opt:bound_shape_inference_test - BoundShapeInference.Int8QuantizeInferInputBackwards (2.705)
    ✓ Pass: caffe2/caffe2/opt:bound_shape_inference_test - BoundShapeInference.SparseLengthsSum (2.729)
    ✓ Pass: caffe2/caffe2/opt:bound_shape_inference_test - BoundShapeInference.Reshape (2.754)
    ✓ Pass: caffe2/caffe2/opt:bound_shape_inference_test - BoundShapeInference.ConcatMissingInput (2.770)
    ✓ Pass: caffe2/caffe2/opt:bound_shape_inference_test - BoundShapeInference.ElementwiseOp (2.770)
    ✓ Pass: caffe2/caffe2/opt:bound_shape_inference_test - BoundShapeInference.Tile (2.785)
    ✓ Pass: caffe2/caffe2/opt:bound_shape_inference_test - BoundShapeInference.Bucketize (2.789)
    ✓ Pass: caffe2/caffe2/opt:bound_shape_inference_test - BoundShapeInference.SparseLengthsSumFused8BitRowwise (2.807)
    ✓ Pass: caffe2/caffe2/opt:bound_shape_inference_test - BoundShapeInference.SparseLengthsSum8BitRowwiseSparse (2.841)
    ✓ Pass: caffe2/caffe2/opt:bound_shape_inference_test - BoundShapeInference.Split (2.863)
    ✓ Pass: caffe2/caffe2/opt:bound_shape_inference_test - BoundShapeInference.ConcatInferInputBackwards (2.894)
    ✓ Pass: caffe2/caffe2/opt:bound_shape_inference_test - BoundShapeInference.ElementwiseInferInputBackwards (2.898)
    ✓ Pass: caffe2/caffe2/opt:bound_shape_inference_test - BoundShapeInference.Combo0 (2.902)
    ✓ Pass: caffe2/caffe2/opt:bound_shape_inference_test - BoundShapeInference.LengthsRangeFill (2.964)
    ✓ Pass: caffe2/caffe2/opt:bound_shape_inference_test - BoundShapeInference.Quantization (2.964)
Summary
  Pass: 18
  ListingSuccess: 1
Finished test run: https://our.intern.facebook.com/intern/testinfra/testrun/844425102187909
```

buck test caffe2/caffe2/fb/opt:bound_shape_inference_net_test

```
 Started reporting to test run: https://our.intern.facebook.com/intern/testinfra/testrun/3096224780078093
    ✓ ListingSuccess: caffe2/caffe2/fb/opt:bound_shape_inference_net_test - main (14.092)
    ✓ Pass: caffe2/caffe2/fb/opt:bound_shape_inference_net_test - BoundShapeInference.ClipLengths (15.508)
    ✓ Pass: caffe2/caffe2/fb/opt:bound_shape_inference_net_test - BoundShapeInference.DPER3IdListFeaturePreProcessing (15.521)
    ✓ Pass: caffe2/caffe2/fb/opt:bound_shape_inference_net_test - BoundShapeInference.ClipRanges (16.198)
    ✓ Pass: caffe2/caffe2/fb/opt:bound_shape_inference_net_test - BoundShapeInference.RowwisePrune (16.302)
    ✓ Pass: caffe2/caffe2/fb/opt:bound_shape_inference_net_test - FbBoundShapeInferencerTest.GatherRanges1 (16.585)
    ✓ Pass: caffe2/caffe2/fb/opt:bound_shape_inference_net_test - BoundShapeInference.Combo3 (16.865)
    ✓ Pass: caffe2/caffe2/fb/opt:bound_shape_inference_net_test - BoundShapeInference.DPER3IdListFeaturePreProcessingWithCast (16.907)
    ✓ Pass: caffe2/caffe2/fb/opt:bound_shape_inference_net_test - BoundShapeInference.GatherRanges2 (16.921)
    ✓ Pass: caffe2/caffe2/fb/opt:bound_shape_inference_net_test - FbBoundShapeInferencerTest.LengthsRangeFill (17.157)
    ✓ Pass: caffe2/caffe2/fb/opt:bound_shape_inference_net_test - BoundShapeInference.ClipRangesAndGatherRanges (17.277)
    ✓ Pass: caffe2/caffe2/fb/opt:bound_shape_inference_net_test - BoundShapeInference.DPER3IdScoreListFeaturePreProcessing (17.274)
    ✓ Pass: caffe2/caffe2/fb/opt:bound_shape_inference_net_test - BoundShapeInference.ClipRangesGatherSigridHash (17.554)
    ✓ Pass: caffe2/caffe2/fb/opt:bound_shape_inference_net_test - BoundShapeInference.Combo1 (17.645)
    ✓ Pass: caffe2/caffe2/fb/opt:bound_shape_inference_net_test - BoundShapeInference.DPER3IdScoreListFeaturePreProcessingDEFAULT (17.887)
    ✓ Pass: caffe2/caffe2/fb/opt:bound_shape_inference_net_test - BoundShapeInference.DPER3IdListFeaturePreProcessingDEFAULT (17.929)
    ✓ Pass: caffe2/caffe2/fb/opt:bound_shape_inference_net_test - BoundShapeInference.f97293388_0 (19.343)
    ✓ Pass: caffe2/caffe2/fb/opt:bound_shape_inference_net_test - FbBoundShapeInferencerTest.GatherRangesToDense1 (19.489)
    ✓ Pass: caffe2/caffe2/fb/opt:bound_shape_inference_net_test - BoundShapeInference.DPER3IdScoreListFeaturePreProcessingWithCast (19.887)
    ✓ Pass: caffe2/caffe2/fb/opt:bound_shape_inference_net_test - BoundShapeInference.xray_v11 (19.905)
    ✓ Pass: caffe2/caffe2/fb/opt:bound_shape_inference_net_test - FbBoundShapeInferencerTest.SigridTransforms (20.080)
    ✓ Pass: caffe2/caffe2/fb/opt:bound_shape_inference_net_test - BoundShapeInference.Combo2 (20.086)
    ✓ Pass: caffe2/caffe2/fb/opt:bound_shape_inference_net_test - BoundShapeInference.vanillaSparseNN (59.847)
    ✓ Pass: caffe2/caffe2/fb/opt:bound_shape_inference_net_test - BoundShapeInference.gather (97.822)
Summary
  Pass: 23
  ListingSuccess: 1
```

## Workflow testing

===
* non-DI/fixed quantile/user side/non-self-binning
f224250571

*  non-DI/fixed quantile/user+ad side/non-self-binning
f224250610

* DI/fixed quantile/user side/self-binning
f224250637

* DI/fixed quantile/user+ad side/self-binning
f224250662

*  non-DI/dynamic quantile/user+ad side/non-self-binning
f224250705

* DI/dynamic quantile/user+ad side/self-binning
f224250760

Reviewed By: ChunliF

Differential Revision: D23647390

fbshipit-source-id: 3ec1c0eaea53bd4d5eda4a0436577216f7fa8ead",2020-10-15T07:43:53Z,Zeliang Chen
7af6f9515f50a73126bf1fd59c1561600879dacb,"Move TensorAccessor to ATen/core

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/11014

Reviewed By: cpuhrsch

Differential Revision: D9561802

fbshipit-source-id: d3dbe6d7e76e2419ead81fb448711f101daee19f",2018-09-02T04:38:49Z,Edward Yang
3c977fb7cea9aeb2895166a13ed8ac13fd85fb1e,"Error out on in-place (unary) ops on tensors that have internal overlap (#17927)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/17927
ghimport-source-id: 626d321e430b6b5c0ea3aa1eb9df8c1e2d058bf8

Stack:
* #17926 Implement at::has_internal_overlap helper function
* **#17927 Error out on in-place (unary) ops on tensors that have internal overlap**

On the way to #17935.

Works for CPU and CUDA on the following ops:
- abs_, acos_, asin_, atan_, ceil_, cos_, erf_, erfc_, exp_, expm1_
- floor_, log_, log10_, log1p_, log2_, round_, rsqrt_,
- sin_, sqrt_, tan_, tanh_, trunc_

This PR adds a check to see if the out/result tensor has internal
overlap. If it does, then we error out because the result **may** be
incorrect.

This is overly conservative; there are some cases where if the result is
the same as the input, the inplace operation is OK (such as floor_,
round_, and trunc_). However, the current code isn't organized in such a
way that this is easy to check, so enabling those will come in the future.

Reviewed By: ezyang

Differential Revision: *********

fbshipit-source-id: 15e12bf1fdb2ab7f74bb806e22bc74840bd6abd1",2019-03-15T14:41:08Z,Richard Zou
50e73a8313fbdb85d1be296b4890d12709816818,"Support synchronous mode in ibverbs transport

Summary:
Synchronous mode means using the calling thread instead of the device
thread for completion handling. Since this saves a context switch in
the critical path, this is very beneficial for low latency algorithms.

For example: the p99 of a 4-way barrier drops from 17us to 4us.

Reviewed By: andrewwdye

Differential Revision: ********

fbshipit-source-id: 013b1680497589fe5ad0bca38600bce6a410200b",2017-02-28T20:34:49Z,Pieter Noordhuis
9d4731f952e1c414bf5fe6d14190b7338a883dc3,"[AOTI] Disable stack allocation for OSS (#125732)

Summary: Stack allocation is for certain small CPU models, but its coverage still needs improvement, so default to OFF for OSS.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/125732
Approved by: https://github.com/chenyang78
ghstack dependencies: #126720, #126801",2024-05-24T14:21:27Z,Bin Bao
d478605dec65a746d41506b23693d6013bfa11b2,"Fix classmethod override argument passing. (#47114)

Summary:
Fixes https://github.com/pytorch/pytorch/issues/47069.
Fixes https://github.com/pytorch/pytorch/issues/46824.
Fixes https://github.com/pytorch/pytorch/issues/47186

Pull Request resolved: https://github.com/pytorch/pytorch/pull/47114

Reviewed By: ngimel

Differential Revision: D24649598

Pulled By: ezyang

fbshipit-source-id: af077affece7eceb1e4faf9c94d15484796b0f0e",2020-11-11T17:23:02Z,Hameer Abbasi
421f40e051431c377a3a541fb5a8dbaba61c0dd6,"Use binary units for CUDA memory summary (#91854)

To reduce confusion, use for example `KiB` instead of `KB` since we're talking powers of 2 and not 10.

https://en.wikipedia.org/wiki/Byte#Multiple-byte_units

```
import torch
x = torch.zeros(1024 * 1024, dtype=torch.uint8, device='cuda')
print(torch.cuda.memory_summary())
```

```
|===========================================================================|
|                  PyTorch CUDA memory summary, device ID 0                 |
|---------------------------------------------------------------------------|
|            CUDA OOMs: 0            |        cudaMalloc retries: 0         |
|===========================================================================|
|        Metric         | Cur Usage  | Peak Usage | Tot Alloc  | Tot Freed  |
|---------------------------------------------------------------------------|
| Allocated memory      |   1024 KiB |   1024 KiB |   1024 KiB |      0 B   |
|       from large pool |      0 KiB |      0 KiB |      0 KiB |      0 B   |
|       from small pool |   1024 KiB |   1024 KiB |   1024 KiB |      0 B   |
|---------------------------------------------------------------------------|
| Active memory         |   1024 KiB |   1024 KiB |   1024 KiB |      0 B   |
|       from large pool |      0 KiB |      0 KiB |      0 KiB |      0 B   |
|       from small pool |   1024 KiB |   1024 KiB |   1024 KiB |      0 B   |
|---------------------------------------------------------------------------|
```
Pull Request resolved: https://github.com/pytorch/pytorch/pull/91854
Approved by: https://github.com/ngimel",2023-01-14T05:10:47Z,milesial
9f17037e8ba7d98d8fbbcf40625fcad48c4c0b16,"[dtensor] move tensor constructors to the api module (#133129)

This is to ensure __init__.py only contain public APIs

Pull Request resolved: https://github.com/pytorch/pytorch/pull/133129
Approved by: https://github.com/awgu, https://github.com/tianyu-l",2024-08-13T02:05:08Z,Wanchao Liang
d4a94ad0413db201519eb55bd8f3d80e9956bea6,"[ONNX] Fix upsample_bilinear2d decomp skip with output shape (#118823)

The previous output size missed the first two dimensions.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/118823
Approved by: https://github.com/titaiwangms",2024-02-01T22:04:35Z,BowenBao
002d4f9f7d61fa8ef2f8a63f0e76903a11df7517,"Erase shape information from class types (#23362)

Summary:
](https://our.intern.facebook.com/intern/diff/16681944/)
Pull Request resolved: https://github.com/pytorch/pytorch/pull/23362

Pulled By: driazati

Differential Revision: D16681944

fbshipit-source-id: dba46b6fc3223a2f94dc502531df438f3212d8fb",2019-08-07T05:21:30Z,davidriazati
bda40639c53a96cecc9eb1c21d47257d927ebe74,"[nnc] Move operator implementations into a subdirectory (#59988)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/59988

As we broaden operator support, putting all the implementations into
kernel.cpp is getting unwieldy.  Let's factor them out into the ""operators""
subdirectory.

This diff is big but it's entirely code movement; I didn't change anything,
other than to expose a few utilities in kernel.h.
ghstack-source-id: 131405139

Test Plan: CI

Reviewed By: ZolotukhinM

Differential Revision: D29115916

fbshipit-source-id: ba0df1d8dd4a108b584da3baf168407e966b2c78",2021-06-16T12:07:43Z,Bert Maher
d0ad696f9d5319730d91a450011cd9ee1fde3c2f,"Warn about THPObjectPtr needing GIL. (#9265)

Summary:
Signed-off-by: Edward Z. Yang <<EMAIL>>
Pull Request resolved: https://github.com/pytorch/pytorch/pull/9265

Differential Revision: D8767687

Pulled By: ezyang

fbshipit-source-id: 900b37f2749112cafc5b48e7b444a256df18186a",2018-07-09T20:44:51Z,Edward Z. Yang
96651458eb97a1898a307a0f7c03fa963b4cc7d3,"Automated submodule update: tensorpipe (#59374)

Summary:
This is an automated pull request to update the first-party submodule for [pytorch/tensorpipe](https://github.com/pytorch/tensorpipe).

New submodule commit: https://github.com/pytorch/tensorpipe/commit/e942ea15138d1ca5eac08b194858b54fa1e4ab2f

Pull Request resolved: https://github.com/pytorch/pytorch/pull/59374

Test Plan: Ensure that CI jobs succeed on GitHub before landing.

Reviewed By: lw

Differential Revision: D28867855

fbshipit-source-id: e1325046003f5c546f02024ff4c427c91721cd7e",2021-06-10T11:39:49Z,Facebook Community Bot
8c927b208ca9ca5b8bac481f9b54d104038def3c,"improve test_docs_coverage error messages (#21029)

Summary:
Most important fix: Correct ""tensor.rst"" to ""tensors.rst""

Secondary fix: some minor English spelling/grammar fixes.
Pull Request resolved: https://github.com/pytorch/pytorch/pull/21029

Differential Revision: D15523230

Pulled By: umanwizard

fbshipit-source-id: 6052d8609c86efa41a4289cd3a099b2f1037c810",2019-05-31T18:09:37Z,Brennan Vincent
d96aac8d2ae231070c6d0613f39f02c342dfbcd5,"[MPS] Add logit op (#95162)

Fixes #ISSUE_NUMBER

Pull Request resolved: https://github.com/pytorch/pytorch/pull/95162
Approved by: https://github.com/kulinseth",2023-02-21T07:02:45Z,Li-Huai (Allan) Lin
8dcd256201a16827bae3610fe05f6566cce787d0,"Memory layout for pooling ops

Summary: Pull Request resolved: https://github.com/pytorch/pytorch/pull/25374

Test Plan: Imported from OSS

Differential Revision: D17107577

Pulled By: jamesr66a

fbshipit-source-id: e40dacaddf5ee17e6483be9e9302d3afc1a708c7",2019-08-29T18:41:33Z,James Reed
8da18af6ce66a3cec925065a52674b0b679e1a8e,update to new storage mmap api,2014-04-07T12:54:09Z,Ronan Collobert
232530cc28bce864e04ab7af2a43873c37226a3a,Move scalar tests from common_nn to legacy_nn. (#5223),2018-02-13T21:44:21Z,gchanan
d38a71d579dc2f03d7a35132ccb168e697c1838e,"`torch.nn.modules.LazyModuleMixin` and `torch.nn.LazyLinear` (Shape Inference II) (#44538)

Summary:
Retake on https://github.com/pytorch/pytorch/issues/40493 after all the feedback from albanD

This PR implements the generic Lazy mechanism and a sample `LazyLinear` layer with the `UninitializedParameter`.

The main differences with the previous PR are two;
Now `torch.nn.Module` remains untouched.
We don't require an explicit initialization or a dummy forward pass before starting the training or inference of the actual module. Making this much simpler to use from the user side.

As we discussed offline, there was the suggestion of not using a mixin, but changing the `__class__` attribute of `LazyLinear` to become `Linear` once it's completely initialized. While this can be useful, by the time being we need `LazyLinear` to be a `torch.nn.Module` subclass since there are many checks that rely on the modules being instances of `torch.nn.Module`.
This can cause problems when we create complex modules such as
```
class MyNetwork(torch.nn.Module):
    def __init__(self):
        super(MyNetwork, self).__init__()
        self.conv = torch.nn.Conv2d(20, 4, 2)
        self.linear = torch.nn.LazyLinear(10)
    def forward(self, x):
        y = self.conv(x).clamp(min=0)
        return self.linear(y)
```
Here, when the __setattr__ function is called at the time LazyLinear is registered, it won't be added to the child modules of `MyNetwork`, so we have to manually do it later, but currently there is no way to do such thing as we can't access the parent module from LazyLinear once it becomes the Linear module. (We can add a workaround to this if needed).

TODO:

Add convolutions once the design is OK
Fix docstrings

Pull Request resolved: https://github.com/pytorch/pytorch/pull/44538

Reviewed By: ngimel

Differential Revision: D24162854

Pulled By: albanD

fbshipit-source-id: 6d58dfe5d43bfb05b6ee506e266db3cf4b885f0c",2020-10-19T20:09:16Z,Emilio Castillo
99608ceed660fb308f3ac1226c99be349c3f0b9c,"Scoped extension building for C++ backed custom ops tests (#136695)

FIXES #125579 #131103 #133197 #133283 #134738 #135369 #135685

Tests that create C++ extensions can cause flakiness in CI due to library namespace conflict and test ordering. We can build them in temp dirs to ensure isolation.

An alternative is to build these as part of the build process and have build time errors.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/136695
Approved by: https://github.com/zou3519",2024-10-25T20:19:47Z,Simon Fan
e568b3fa2d8efcc9a6c43f15520a90aa98c134e6,"test nan and inf in TestTorchMathOps (#41225)

Summary:
Per title. `lgamma` produces a different result for `-inf` compared to scipy, so there comparison is skipped.

Pull Request resolved: https://github.com/pytorch/pytorch/pull/41225

Differential Revision: D22473346

Pulled By: ngimel

fbshipit-source-id: e4ebda1b10e2a061bd4cef38d1d7b5bf0f581790",2020-07-10T16:44:58Z,Natalia Gimelshein
cb9ef4668ed37460d99cc8ee3d9960fef2075902,"Updated library level maintainers for torchtext (#84950)

- Updated library level maintainers for torchtext to reflect internal changes to the team

Pull Request resolved: https://github.com/pytorch/pytorch/pull/84950
Approved by: https://github.com/mthrok",2022-09-14T00:35:36Z,Nayef Ahmed
e4c41b6936ed433aff8e60735eba938ba66334e8,"Remove codegen logic to support non-c10-full ops (#49164)

Summary:
Pull Request resolved: https://github.com/pytorch/pytorch/pull/49164

This PR removes the logic paths in codegen that were responsible for handling non-c10-full ops.
This only goes through our basic codegen. It does not simplify C++ code yet and it does not remove the codegen for generated unboxing wrappers yet.
ghstack-source-id: 119450487

Test Plan: waitforsandcastle

Reviewed By: ezyang

Differential Revision: *********

fbshipit-source-id: 7e70d14bea96948f5056d98125f3e6ba6bd78285",2021-01-06T22:14:24Z,Sebastian Messmer
5d45140d6874be04c22c8abba55e4438c25d2fdb,"[numpy] torch.{all/any} : output dtype is always bool (#47878)

Summary:
BC-breaking note:

This PR changes the behavior of the any and all functions to always return a bool tensor. Previously these functions were only defined on bool and uint8 tensors, and when called on uint8 tensors they would also return a uint8 tensor. (When called on a bool tensor they would return a bool tensor.)

PR summary:

https://github.com/pytorch/pytorch/pull/44790#issuecomment-725596687

Fixes 2 and 3

Also Fixes https://github.com/pytorch/pytorch/issues/48352

Changes
* Output dtype is always `bool` (consistent with numpy) **BC Breaking (Previously used to match the input dtype**)
* Uses vectorized version for all dtypes on CPU
* Enables test for complex
* Update doc for `torch.all` and `torch.any`

TODO
* [x] Update docs
* [x] Benchmark
* [x] Raise issue on XLA

Pull Request resolved: https://github.com/pytorch/pytorch/pull/47878

Reviewed By: albanD

Differential Revision: D25714324

Pulled By: mruberry

fbshipit-source-id: a87345f725297524242d69402dfe53060521ea5d",2021-01-08T19:01:52Z,kshitij12345
