from abc import ABC, abstractmethod
from typing import List, Dict, Any
import pandas as pd

class BaseDataset(ABC):
    """极简数据集接口，专注数据格式标准化"""
    
    @abstractmethod
    def load_data(self) -> pd.DataFrame:
        """加载原始数据，返回DataFrame"""
        pass
    
    @abstractmethod
    def format_prompt(self, row: pd.Series) -> str:
        """将数据行格式化为prompt"""
        pass
    
    def get_prompts(self) -> List[Dict[str, Any]]:
        """获取格式化的prompts列表"""
        df = self.load_data()
        return [
            {
                "id": str(idx),
                "prompt": self.format_prompt(row),
                "metadata": row.to_dict()
            }
            for idx, row in df.iterrows()
        ]
    
    def get_dataset_name(self) -> str:
        """获取数据集名称"""
        return self.__class__.__name__.replace("Dataset", "").lower()

class TrivialQADataset(BaseDataset):
    """TrivialQA数据集实现"""
    
    def __init__(self, data_path: str = None, sample_size: int = None):
        self.data_path = data_path or "./data/trivialqa.json"
        self.sample_size = sample_size
    
    def load_data(self) -> pd.DataFrame:
        """加载TrivialQA数据"""
        # 这里需要根据实际数据格式调整
        try:
            df = pd.read_json(self.data_path)
            if self.sample_size:
                df = df.sample(n=min(self.sample_size, len(df)), random_state=42)
            return df
        except FileNotFoundError:
            # 创建示例数据
            return pd.DataFrame([
                {"question": "What is the capital of France?", "answer": "Paris"},
                {"question": "Who wrote Romeo and Juliet?", "answer": "Shakespeare"},
                {"question": "What is 2+2?", "answer": "4"}
            ])
    
    def format_prompt(self, row: pd.Series) -> str:
        """格式化为问答prompt"""
        return f"Question: {row['question']}\nAnswer:"