"""
TrivialQA dataset implementation for uncertainty analysis.

This module provides access to TrivialQA data for uncertainty quantification tasks.
"""

import json
import os
from typing import List, Dict, Any
from ..base_dataset import BaseDataset


class TrivialQADataset(BaseDataset):
    """TrivialQA dataset for uncertainty analysis."""
    
    def __init__(self, split: str = "test", data_dir: str = ""):
        super().__init__("trivialqa", split, data_dir)
    
    def load_data(self) -> List[Dict[str, Any]]:
        """Load TrivialQA data from JSONL files."""
        file_path = os.path.join(self.data_dir, f"triviaqa-{self.split}.jsonl")
        
        if not os.path.exists(file_path):
            # Create sample data if files don't exist
            self._create_sample_data()
            return self.data
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                self.data = [json.loads(line.strip()) for line in f if line.strip()]
        except FileNotFoundError:
            self._create_sample_data()
        except json.JSONDecodeError as e:
            print(f"Error loading {file_path}: {e}")
            self._create_sample_data()
        
        return self.data
    
    def _create_sample_data(self):
        """Create sample TrivialQA data for testing."""
        sample_data = [
            {
                "question_id": "tqa_000",
                "question": "What is the type of this commit based on the commit message? Message: 'Create a new unstable workflow for periodic jobs (#98858)\n\nAnd move ROCm distributed job there as it's very flaky in trunk at the moment.  Also move ROCm slow job to `slow` workflow as it should be.\n\nPull Request resolved: https://github.com/pytorch/pytorch/pull/98858\nApproved by: https://github.com/malfet, https://github.com/ZainRizvi' ",
                "answer": "Feature",
                "category": "Software Development",
                "difficulty": "medium"
            },
            {
                "question_id": "tqa_001",
                "question": "What is the capital of France?",
                "answer": "Paris",
                "category": "Geography",
                "difficulty": "easy"
            },
            {
                "question_id": "tqa_002", 
                "question": "Who wrote 'Romeo and Juliet'?",
                "answer": "William Shakespeare",
                "category": "Literature",
                "difficulty": "medium"
            },
            {
                "question_id": "tqa_003",
                "question": "What is 2 + 2?",
                "answer": "4",
                "category": "Mathematics",
                "difficulty": "easy"
            },
            {
                "question_id": "tqa_004",
                "question": "What planet is known as the Red Planet?",
                "answer": "Mars",
                "category": "Science",
                "difficulty": "easy"
            },
            {
                "question_id": "tqa_005",
                "question": "In which year did World War II end?",
                "answer": "1945",
                "category": "History",
                "difficulty": "medium"
            },
            {
                "question_id": "tqa_006",
                "question": "What is the largest mammal?",
                "answer": "Blue whale",
                "category": "Science",
                "difficulty": "medium"
            },
            {
                "question_id": "tqa_007",
                "question": "What language is primarily spoken in Brazil?",
                "answer": "Portuguese",
                "category": "Geography",
                "difficulty": "easy"
            },
            {
                "question_id": "tqa_008",
                "question": "How many continents are there?",
                "answer": "7",
                "category": "Geography",
                "difficulty": "easy"
            },
            {
                "question_id": "tqa_009",
                "question": "Who painted the Mona Lisa?",
                "answer": "Leonardo da Vinci",
                "category": "Art",
                "difficulty": "medium"
            },
            {
                "question_id": "tqa_010",
                "question": "What is the chemical symbol for gold?",
                "answer": "Au",
                "category": "Science",
                "difficulty": "medium"
            },
            {
                "question_id": "tqa_011",
                "question": "Which country is home to the kangaroo?",
                "answer": "Australia",
                "category": "Geography",
                "difficulty": "easy"
            },
            {
                "question_id": "tqa_012",
                "question": "What is the smallest prime number?",
                "answer": "2",
                "category": "Mathematics",
                "difficulty": "easy"
            },
            {
                "question_id": "tqa_013",
                "question": "Who wrote '1984'?",
                "answer": "George Orwell",
                "category": "Literature",
                "difficulty": "medium"
            },
            {
                "question_id": "tqa_014",
                "question": "What is the main ingredient in guacamole?",
                "answer": "Avocado",
                "category": "Food",
                "difficulty": "easy"
            },
            {
                "question_id": "tqa_015",
                "question": "In which sport would you perform a slam dunk?",
                "answer": "Basketball",
                "category": "Sports",
                "difficulty": "easy"
            }
        ]
        
        self.data = sample_data
        
        # Save sample data to file for future use
        os.makedirs(self.data_dir, exist_ok=True)
        with open(os.path.join(self.data_dir, f"triviaqa-{self.split}.jsonl"), 'w', encoding='utf-8') as f:
            for item in sample_data:
                f.write(json.dumps(item, ensure_ascii=False) + '\n')
    
    def get_metadata(self) -> Dict[str, Any]:
        """Get dataset metadata."""
        metadata = super().get_metadata()
        metadata.update({
            "description": "TrivialQA dataset for uncertainty analysis",
            "source": "https://nlp.cs.washington.edu/triviaqa/",
            "license": "Research use",
            "fields": ["question_id", "question", "answer", "category", "difficulty"]
        })
        return metadata