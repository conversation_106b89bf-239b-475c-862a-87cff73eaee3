# LLM不确定性评估框架

一个完整的LLM不确定性量化评估框架，支持多种数据集和UQ方法的无缝集成。

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 设置环境变量

创建一个 `.env` 文件并添加你的OpenAI API密钥：

```bash
echo "OPENAI_API_KEY=your_api_key_here" > .env
```

### 3. 运行实验

```bash
python run_experiment.py --config config/experiment.yaml
```

### 4. 恢复中断实验

```bash
python run_experiment.py --config config/experiment.yaml --resume
```

## 📁 项目结构

```
llm-uncertainty/
├── core/                          # 核心组件
│   ├── llm_client.py             # OpenAI统一客户端
│   ├── cache.py                  # 智能缓存系统
│   ├── parallel.py               # 并行处理
│   ├── progress.py               # 进度可视化
│   └── checkpoint.py             # 断点续跑
├── datasets/                     # 数据集模块
│   ├── base.py                   # 数据集基类
│   └── implementations/          # 具体实现
├── uq_methods/                   # UQ方法模块
│   ├── base.py                   # UQ方法基类
│   └── implementations/          # 具体实现
├── prompts/                      # Prompt管理
│   └── manager.py                # Prompt模板管理
├── results/                      # 结果导出
│   └── csv_exporter.py           # CSV导出系统
├── config/                       # 配置文件
│   └── experiment.yaml           # 实验配置
├── run_experiment.py             # 主运行脚本
└── dev_tools.py                  # 开发工具
```

## 🛠️ 使用示例

### 创建新的数据集

```bash
python dev_tools.py create_dataset my_dataset --description "我的自定义数据集"
```

### 创建新的UQ方法

```bash
python dev_tools.py create_uq_method my_method --description "我的不确定性方法"
```

### 创建配置文件

```bash
python dev_tools.py create_config my_experiment --model gpt-4
```

### 查看实验列表

```bash
python dev_tools.py list_experiments
```

### 清理实验检查点

```bash
python dev_tools.py cleanup experiment_id_here
```